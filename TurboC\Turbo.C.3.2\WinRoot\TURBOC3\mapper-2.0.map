hand_shutdown 
hand_cap<PERSON>e "key 291 mod1" 
hand_fullscr "key 13 mod2" 
hand_pause "key 19 mod2" 
hand_mapper "key 282 mod1" 
hand_speedlock "key 293 mod2" 
hand_recwave "key 287 mod1" 
hand_capraw<PERSON>di "key 289 mod1 mod2" 
hand_decfskip "key 288 mod1" 
hand_incfskip "key 289 mod1" 
hand_cycledown "key 292 mod1" 
hand_cycleup "key 293 mod1" 
hand_caprawopl "key 288 mod1 mod2" 
hand_swapimg "key 285 mod1" 
key_esc "key 27" 
key_f1 "key 282" 
key_f2 "key 283" 
key_f3 "key 284" 
key_f4 "key 285" 
key_f5 "key 286" 
key_f6 "key 287" 
key_f7 "key 288" 
key_f8 "key 289" 
key_f9 "key 290" 
key_f10 "key 291" 
key_f11 "key 292" 
key_f12 "key 293" 
key_grave "key 96" 
key_1 "key 49" 
key_2 "key 50" 
key_3 "key 51" 
key_4 "key 52" 
key_5 "key 53" 
key_6 "key 54" 
key_7 "key 55" 
key_8 "key 56" 
key_9 "key 57" 
key_0 "key 48" 
key_minus "key 45" 
key_equals "key 61" 
key_bspace "key 8" 
key_tab "key 9" 
key_q "key 113" 
key_w "key 119" 
key_e "key 101" 
key_r "key 114" 
key_t "key 116" 
key_y "key 121" 
key_u "key 117" 
key_i "key 105" 
key_o "key 111" 
key_p "key 112" 
key_lbracket "key 91" 
key_rbracket "key 93" 
key_enter "key 13" 
key_capslock "key 301" 
key_a "key 97" 
key_s "key 115" 
key_d "key 100" 
key_f "key 102" 
key_g "key 103" 
key_h "key 104" 
key_j "key 106" 
key_k "key 107" 
key_l "key 108" 
key_semicolon "key 59" 
key_quote "key 39" 
key_backslash "key 92" 
key_lshift "key 304" 
key_lessthan "key 60" 
key_z "key 122" 
key_x "key 120" 
key_c "key 99" 
key_v "key 118" 
key_b "key 98" 
key_n "key 110" 
key_m "key 109" 
key_comma "key 44" 
key_period "key 46" 
key_slash "key 47" 
key_rshift "key 303" 
key_lctrl "key 306" 
key_lalt "key 308" 
key_space "key 32" 
key_ralt "key 307" 
key_rctrl "key 305" 
key_printscreen "key 316" 
key_scrolllock "key 302" 
key_pause "key 19" 
key_insert "key 277" 
key_home "key 278" 
key_pageup "key 280" 
key_delete "key 127" 
key_end "key 279" 
key_pagedown "key 281" 
key_up "key 273" 
key_left "key 276" 
key_down "key 274" 
key_right "key 275" 
key_numlock "key 300" 
key_kp_divide "key 267" 
key_kp_multiply "key 268" 
key_kp_minus "key 269" 
key_kp_7 "key 263" 
key_kp_8 "key 264" 
key_kp_9 "key 265" 
key_kp_plus "key 270" 
key_kp_4 "key 260" 
key_kp_5 "key 261" 
key_kp_6 "key 262" 
key_kp_1 "key 257" 
key_kp_2 "key 258" 
key_kp_3 "key 259" 
key_kp_enter "key 271" 
key_kp_0 "key 256" 
key_kp_period "key 266" 
jbutton_0_0 
jbutton_0_1 
jaxis_0_1- 
jaxis_0_1+ 
jaxis_0_0- 
jaxis_0_0+ 
jbutton_0_2 
jbutton_0_3 
jbutton_1_0 
jbutton_1_1 
jaxis_0_2- 
jaxis_0_2+ 
jaxis_0_3- 
jaxis_0_3+ 
jaxis_1_0- 
jaxis_1_0+ 
jaxis_1_1- 
jaxis_1_1+ 
jbutton_0_4 
jbutton_0_5 
jhat_0_0_0 
jhat_0_0_3 
jhat_0_0_2 
jhat_0_0_1 
mod_1 "key 306" "key 305" 
mod_2 "key 308" "key 307" 
mod_3 
