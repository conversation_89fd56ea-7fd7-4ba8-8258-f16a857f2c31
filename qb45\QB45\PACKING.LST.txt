PACKING.LST File for QuickBASIC Version 4.50 
This package comes with a number of demonstration and utility programs written in BASIC. 
=========================================================================================
******** ROOT directory contents ********
BC.EXE					The BASIC command-line compiler	invoked	by the Run
								menu's Make EXE File command or by the bc command
								from the DOS command line.
BRUN45.EXE			The QuickBASIC run-time	module;	required for running
								executable files created with BRUN45.LIB.
LIB.EXE					The Microsoft Library Manager; used to create
								stand-alone (.LIB) libraries.
LINK.EXE				The Microsoft Overlay Linker; used to create
								executable files and Quick libraries.
QB.EXE					The QuickBASIC program development environment.
QB.INI					The QuickBASIC configuration file.
MOUSE.COM				The Mouse driver for use with QuickBASIC programs
								that call mouse functions.
								
	******** \HLP directory contents ********
	QB45ENER.HLP		File containing	on-line	help information dealing with
									the QuickBASIC environment and error messages.
	QB45QCK.HLP			File containing on-line help on QuickBASIC.
	QB45ADVR.HLP		File containing on-line help information on QuickBASIC.
	
	******** \INC directory contents ********
	DEMO1.BAS				A BASICA version of a sound-effects demonstration
									program.
	DEMO2.BAS				The QuickBASIC 2.0 version of DEMO1.BAS.
	DEMO3.BAS				The QuickBASIC 4.0 (and	higher)	version	of DEMO1.BAS.
	QCARDS.BAS			Supplied code for the QCARDS database program used
									in the Hands On	with QuickBASIC	tutorial. In Part 2
									of the manual Learning to Use QuickBASIC, you add
									the module-level code that completes this program.
	QCARDS.DAT			Supplied data file for the QCARDS database program.
									Keep this file in the current directory	as you add
									code during the	QCARDS.BAS tutorial.
	REMLINE.BAS			A utility program that converts	BASICA programs
									saved in ASCII format to QuickBASIC-style programs
									by removing unreferenced line numbers.
	SORTDEMO.BAS		A program that uses multicolored bars and sound to
									illustrate various sorting algorithms.
	TORUS.BAS				A graphics demonstration program that draws a
									multicolored doughnut-shaped figure on the screen,
									then animates it by shifting colors in the palette.
	QB.BI						An include file	for use	with BASIC programs that
									call any of the	following routines in the QB.QLB
									Quick library or in the	QB.LIB stand-alone library:
									ABSOLUTE, INTERRUPT, INTERRUPTX, INT86OLD, or
									INT86XOLD.  The	QB.BI file defines the types for
									arguments passed to these routines and also gives
									DECLARE	statements for these routines.
	NOEM.OBJ				(NO EMulation).	An object file to link with BASIC
									programs that will always be run on machines with an
									8087 or	80287 math coprocessor chip.  Linking with
									NOEM.OBJ turns off software emulation of the math
									chip's function, and reduces the size of the
									executable file.
	SMALLERR.OBJ		An object file to link with BASIC programs that	do
									not require run-time error messages. Linking with
									SMALLERR.OBJ reduces the size of executable files
									that do	not need run-time error	messages.
	QB.PIF					A file that provides information to aid	in running
									QuickBASIC under Microsoft Windows.

		\ADVR_EX		A directory containing BASIC programs cited in the on-line help.
		******** \ADVR_EX directory contents ********
		CALL_EX.BAS					Illustrates using the CALL statement
		CHR_EX.BAS					Illustrates using the the CHR$ function
		CMD_EX.BAS					Illustrates using the the COMMAND$ function
		COM1_EX.BAS					Illustrates using the the COMMON and CHAIN statements
		COM2_EX.BAS					Module used in COM1_EX.BAS above
		CSR_EX.BAS					Illustrates using the the CSRLIN function
		DECL_EX.BAS					Illustrates using the DECLARE statement
		DEFFN_EX.BAS				Illustrates using the DEF FN statement
		DEFSG_EX.BAS				Illustrates using the DEF SEG, PEEK, and POKE statements
		DRAW_EX.BAS					Illustrates using the DRAW statement
		FUNC_EX.BAS					Illustrates using FUNCTION...END FUNCTION
		OUT_EX.BAS					Illustrates using the OUT statement
		SHARE_EX.BAS				Illustrates using the SHARED statement
		SHELL_EX.BAS				Illustrates using the SHELL statement
		STAT_EX.BAS					Illustrates using the STATIC statement
		SUB_EX.BAS					Illustrates using SUB...END SUB
		TYPE_EX.BAS					Illustrates using TYPE..END TYPE
		UBO_EX.BAS					Illustrates using the UBOUND and LBOUND functions
		UCASE_EX.BAS				Illustrates using the UCASE$ function
		WINDO_EX.BAS				Illustrates using the WINDOW statement

		\EXAMPLES		A directory containing BASIC programs printed in the QuickBASIC manuals and other demonstration programs.
		******** \EXAMPLES directory contents ********
		BALLPSET.BAS				A program that bounces a ball off the bottom and
												sides of the screen by using the PSET option with
												the graphics PUT statement.
		BALLXOR.BAS					A program that bounces a ball off the bottom and
												sides of the screen by using the XOR option with
												the graphics PUT statement.
		BAR.BAS							A program that turns input data	into a bar chart.
		CAL.BAS							A program that prints a	calendar for any month in
												any year from 1899 to 2099.
		CHECK.BAS						A checkbook-balancing program that sorts and prints a
												list of	any deposits and withdrawals input by the
												user, then prints the final balance in the checking
												account.
		COLORS.BAS					A program showing all combinations of the 16 background
												colors and 3 foreground	colors (distinct from the
												background) in the 2 color palettes available in screen
												mode 1.
		CRLF.BAS						A program that opens an	ASCII file, expands any	lines
												ending with just a carriage return or a	line feed to
												a carriage-return--line-feed combination, then writes
												the adjusted lines to a	new file.
		CUBE.BAS						A program that illustrates simple animation of a
												cube by	using multiple screen pages in screen mode 7.
		EDPAT.BAS						A program that allows you to edit a pattern tile
												for use	in a PAINT statement.  With pattern tiles,
												you can	fill any enclosed graphics area	on the screen
												with a pattern.
		ENTAB.BAS						A program that compresses an ASCII file	by replacing
												runs of	spaces with tab	characters.
		FILERR.BAS					A program that searches	for a string of	characters in
												an ASCII file.	This program traps and handles common
												file-access errors such	as the user's entering an
												invalid	file name or leaving a drive door open.
		FLPT.BAS						A program that lets you	examine	the internal format
												used by	BASIC to store single-precision	numbers.
		INDEX.BAS						A file I/O program that	builds and searches an index
												of record numbers from a random-access data file.
		MANDEL.BAS					A program that generates a fractal (a colorful graphic
												representation of the properties of certain real
												numbers) on the	screen.
		PALETTE.BAS					A program that demonstrates how	to give	the illusion
												of movement by rotating	the colors displayed by
												the color attributes from 1 to 15.
		PLOTTER.BAS					A simple line-sketching	program	that uses BASIC's
												DRAW statement.
		QLBDUMP.BAS					A program that allows you to get a listing of the
												PUBLIC code and	data symbols in	a QuickBASIC Quick
												library.
		SEARCH.BAS					A program that searches	any disk file for a pattern
												and reports every byte position	in the file where
												the pattern begins.
		SINEWAVE.BAS				A program that plots the graph of the sine-wave
												function for angle values from 0 to PI radians.
		STRTONUM.BAS				A program that converts	to a numeric value any number
												input as a string, after first filtering invalid
												numeric	characters (such as commas) out	of the
												string.
		TERMINAL.BAS				A program that turns your computer into	a "dumb"
												terminal when used with	a modem.
		TOKEN.BAS						A program that breaks an input string into a series
												of tokens (a string of characters delimited by
												blank spaces, tabs, or punctuation marks such as
												commas or semicolons).
		WHEREIS.BAS					A program that recursively searches through all
												directories on a disk for a specified file name.
	
	******** \LIB directory contents ********												
	BRUN45.LIB			The QuickBASIC run-time-module library;	used for
									creating executable files from QuickBASIC and DOS.
	BQLB45.LIB			The library of supporting routines that	are used when
									creating Quick libraries.
	BCOM45.LIB			The QuickBASIC alternate run-time-module library;
									used for creating executable files from	QuickBASIC
									and DOS	(files created with this library do not
									require	BRUN45.EXE to run).
	QB.LIB					The stand-alone	library	containing support routines
									for DOS system calls.
	QB.QLB					The Quick library containing support routines for
									DOS system calls.
