Starting with version 0.65, DOSBox allows you to create movies out of screen
output.

To record a movie, you have to press CTRL-ALT-F5.
To stop/end the recording, you have to press CTRL-ALT-F5 again.

To play the recorded movie, you need a movie player which can handle the
ZMBV codec. MS Windows users can find this codec in the start menu entry of
DOSBox. Users of Linux and other OSes should look for a movie player that
uses the ffmpeg libary (you may need to update or ask your distribution to
upgrade).

FAQ:
Q: During the display of the movies the sound is lagging.
A: Check your display properties to see whether your refresh rate is set to
at least 70 hz. Try playing the movie in virtualdub (http://virtualdub.sf.net)

Q: Why does the resulting movie consist of multiple files?
A: Each time the game changes resolution, DOSBox creates a new movie file,
because a movie file can only contain one resolution.

Q: Can I set the cycles higher than my PC can handle during recording?
A: Yes. During recording, the game might play slowly and stuttering, but the
resulting movie should play at the intended speed and have no stuttering.

Q: CTRL-ALT-F5 switches to the console under linux.
A: 1. Start DOSBox like this: dosbox -startmapper
   2. Click on Video, click on Add
   3. Press the key you want (for example scroll lock or printscreen)
   4. Click exit.
   5. You can make movies by pressing scroll lock or whichever key you 
      selected.

Q: The colours are wrong and I'm using 64 bit windows
A: Look here: http://vogons.zetafleet.com/viewtopic.php?t=12133
