




CONTENTS
___________________________________________________________________________





                                           Linker response files . . . . 28
HOW TO USE THIS FILE . . . . . . . 1     Sample OBJXREF reports  . . . . 28
BGIOBJ: Converting graphics drivers        Report by public names
and fonts  . . . . . . . . . . . . 2       (/RP) . . . . . . . . . . . . 29
  Adding the new .OBJ files to             Report by module (/RM)  . . . 29
  GRAPHICS.LIB . . . . . . . . . . 3       Report by reference (/RR) . . 30
  Registering the drivers and              Report by external references
  fonts  . . . . . . . . . . . . . 3       (/RX) . . . . . . . . . . . . 30
    An example . . . . . . . . . . 4       Report of module sizes
  The /F option  . . . . . . . . . 5       (/RS) . . . . . . . . . . . . 31
  Advanced features  . . . . . . . 6       Report by class type (/RC)  . 31
CPP: The preprocessor  . . . . .  10       Report of unreferenced symbol
  CPP as a macro preprocessor  .  11       names (/RU) . . . . . . . . . 32
  An example . . . . . . . . . .  11       Verbose reporting (/RV) . . . 32
GREP: A text-search utility  . .  12     Examples of how to use
  Command-line syntax  . . . . .  13     OBJXREF . . . . . . . . . . . . 32
  GREP options . . . . . . . . .  13       Example 1 . . . . . . . . . . 32
    Order of precedence  . . . .  16       Example 2 . . . . . . . . . . 33
  The search string  . . . . . .  16       Example 3 . . . . . . . . . . 33
    Operators in regular                   Example 4 . . . . . . . . . . 33
    expressions  . . . . . . . .  17     OBJXREF error messages and
  File specifications  . . . . .  18     warnings  . . . . . . . . . . . 34
  Some GREP examples . . . . . .  18       Error messages  . . . . . . . 34
    Example 1  . . . . . . . . .  18       Warnings  . . . . . . . . . . 34
    Example 2  . . . . . . . . .  19   PRJCFG: Configuration file
    Example 3  . . . . . . . . .  19   utility . . . . . . . . . . . . . 35
    Example 4  . . . . . . . . .  20   PRJCNVT: Old projects for new . . 35
    Example 5  . . . . . . . . .  20   PRJ2MAK: From project file to MAKE
    Example 6  . . . . . . . . .  21   file  . . . . . . . . . . . . . . 36
    Example 7  . . . . . . . . .  21   THELP: The Turbo Help utility . . 37
    Example 8  . . . . . . . . .  22     Loading and invoking THELP  . . 37
OBJXREF: The object module cross-        Navigating THELP  . . . . . . . 38
reference utility  . . . . . . .  22     THELP options . . . . . . . . . 39
  The OBJXREF command line . . .  23       /C#xx (select color)  . . . . 40
    The OBJXREF command-line               /Fname (full path and name for
    options  . . . . . . . . . .  24       help file)  . . . . . . . . . 41
      Control options  . . . . .  24       /H, /?, and ? (display help
      Report options . . . . . .  25       screen) . . . . . . . . . . . 42
  Response files . . . . . . . .  27       /Kxxyy (reassign hot key) . . 42
    Free-form response files . .  27       /U (remove THELP from
    Project files  . . . . . . .  27       memory) . . . . . . . . . . . 43



                                     i






    /Wx,y,w,h (set the window size     TRANCOPY: A project transfer item
    and location)  . . . . . . .  43   utility . . . . . . . . . . . . . 52
TLIB . . . . . . . . . . . . . .  43   TRIGRAPH: A character-conversion
  Why use object module                utility . . . . . . . . . . . . . 53
  libraries? . . . . . . . . . .  44   Transfer macros . . . . . . . . . 54
  The TLIB command line  . . . .  44         State macros  . . . . . . . 54
    The operation list . . . . .  46         File name macros  . . . . . 54
      File and module names  . .  46         Instruction macros  . . . . 55
      TLIB operations  . . . . .  46     Running DOS commands  . . . . . 59
  Using response files . . . . .  48     Transfer memory settings  . . . 59
  Creating an extended dictionary:     Turbo Editor macros . . . . . . . 60
  The /E option  . . . . . . . .  49   TEMC command line . . . . . . . . 60
  Setting the page size: The /P        Syntax  . . . . . . . . . . . . . 61
  option . . . . . . . . . . . .  49   Key codes . . . . . . . . . . . . 62
  Advanced operation: The /C             Named keys  . . . . . . . . . . 63
  option . . . . . . . . . . . .  50   Predefined editor commands  . . . 64
  Examples . . . . . . . . . . .  51
TOUCH  . . . . . . . . . . . . .  51   Index                             77

































                                    ii






TABLES
___________________________________________________________________________





1.1: Summary of THELP command-line     1.3: TLIB action symbols  . . . . 47
     options . . . . . . . . . . .39   1.4: TEMC editor commands . . . . 64
1.2: TLIB options  . . . . . . . .45









































                                    iii












Online document
___________________________________________________________________________



                                                            Other utilities




===========================================================================
HOW TO USE THIS FILE
===========================================================================

                    This file has a table of contents and an index that
                    refer to "pages" in this file. If your editor has a
                    search facility, you can use it to search for the page
                    numbers listed in either the table of contents or in
                    the index. The text "- n -" (where n represents the
                    actual page number) appears at the bottom left of the
                    "page" it refers to. Thus, at the bottom of page 1,
                    you'll find "- 1 -" as the last item on that "page."

                    Turbo C++ comes with a host of standalone utilities
                    that you can use with your Turbo C++ files or other
                    modules. Several are documented in the User's Guide;
                    others are documented here.


                    -------------------------------------------------------
                    Name       Description
                    -------------------------------------------------------

                    Documented in the User's Guide

                    MAKE       Standalone program manager
                    TLINK      Turbo Linker

                    Documented in this file

                    BGIOBJ     Conversion utility for graphics drivers and
                               fonts



                                   - 1 -






                    CPP        Preprocessor
                    GREP       File-search utility
                    OBJXREF    Object module cross-referencer
                    PRJCFG     Updates options in a project file from a
                               configuration file, or converts a project
                               file to a configuration file
                    PRJCNVT    Converts Turbo C project files to the Turbo
                               C++ format
                    PRJ2MAK    Converts Turbo C++ project files to MAKE
                               files
                    THELP      Turbo Help utility
                    TLIB       Turbo Librarian
                    TOUCH      Updates file date and time
                    TRANCOPY   Copies transfer items from one project to
                               another
                    TRIGRAPH   Character-conversion utility
                    TEML/TEMC  Turbo Editor Macro Language and Compiler

                    -------------------------------------------------------

                    This file explains what each utility is and
                    illustrates, with code and command-line examples, how
                    to use them.



===========================================================================
BGIOBJ: Converting graphics drivers and fonts
===========================================================================

                    You can use BGIOBJ to convert graphics driver files and
                    character sets (stroked font files) to object (.OBJ)
                    files. Once they're converted, you can link them into
                    your program, making them part of the executable file.
                    This is in addition to the graphics package's dynamic
                    loading scheme, in which your program loads graphics
                    drivers and character sets (stroked fonts) from disk at
                    run time.

                    Linking drivers and fonts directly into your program is
                    advantageous because the executable file contains all
                    (or most) of the drivers and/or fonts it might need,
                    and doesn't need to access the driver and font files on
                    disk when running. However, linking the drivers and
                    fonts into your executable file increases its size.






                                   - 2 -






                    To convert a driver or font file to a linkable object
                    file, use the BGIOBJ.EXE utility. This is the
                    simplified syntax:

                      BGIOBJ source_file

                    where source_file is the driver or font file to be
                    converted to an object file. The object file created
                    has the same file name as the source file, with the
                    extension .OBJ; for example, EGAVGA.BGI yields
                    EGAVGA.OBJ, SANS.CHR gives SANS.OBJ, and so on.


    Adding the new  =======================================================
     .OBJ files to
      GRAPHICS.LIB  You should add the driver and font object modules to
                    GRAPHICS.LIB, so the linker can locate them when it
                    links in the graphics routines. If you don't add these
                    new object modules to GRAPHICS.LIB, you'll have to add
                    them to the list of files in the project (.PRJ) file,
                    on the TCC command line, or on the TLINK command line.
                    To add these object modules to GRAPHICS.LIB, invoke
                    TLIB with the following command line:

                      tlib graphics + object_file_name [+ object_file_name
                    ...]

                    where object_file_name is the name of the object file
                    created by BGIOBJ.EXE (such as CGA, EGAVGA, GOTH, and
                    so forth). The .OBJ extension is implied, so you don't
                    need to include it. You can add several files with one
                    command line to save time; see the example in the
                    following section.


   Registering the  =======================================================
 drivers and fonts
                    After adding driver and font object modules to
                    GRAPHICS.LIB, you have to register all the drivers and
                    fonts that you want linked in; you do this by calling
                    registerbgidriver and registerbgifont in your program
                    (before calling initgraph). This informs the graphics
                    system of the presence of those files, and ensures that
                    they will be linked in when the executable file is
                    created by the linker.

                    The registering routines each take one parameter; a
                    symbolic name defined in graphics.h. Each registering



                                   - 3 -






                    routine returns a nonnegative value if the driver or
                    font is successfully registered.

                    The following table shows the names to be used with
                    registerbgidriver and registerbgifont. It is a complete
                    list of drivers and fonts included with Turbo C++.


---------------------------------------------------------------------------
 Driver file     registerbgidriver      Font file   registerbgifont
 (*.BGI)         symbolic name          (*.CHR)     symbolic name
---------------------------------------------------------------------------

 CGA             CGA_driver             TRIP        triplex_font
 EGAVGA          EGAVGA_driver          LITT        small_font
 HERC            Herc_driver            SANS        sansserif_font
 ATT             ATT_driver             GOTH        gothic_font
 PC3270          PC3270_driver
 IBM8514         IBM8514_driver

---------------------------------------------------------------------------


------------------  Suppose you want to convert the files for the CGA
        An example  graphics driver, the gothic font, and the triplex font
------------------  to object modules, then link them into your program.
                    Here's what you do:

                    1. Convert the binary files to object files using
                       BGIOBJ.EXE, as shown in the following separate com-
                       mand lines:

                        bgiobj  cga
                        bgiobj  trip
                        bgiobj  goth

                       This creates three files: CGA.OBJ, TRIP.OBJ, and
                       GOTH.OBJ.

                    2. You can add these object files to GRAPHICS.LIB with
                       this TLIB command line:

                          tlib  graphics  +cga  +trip  +goth

                    3. If you don't add the object files to GRAPHICS.LIB,
                       you must add the object file names CGA.OBJ,
                       TRIP.OBJ, and GOTH.OBJ to your project list (if you
                       are using Turbo C++'s integrated environment), or to



                                   - 4 -






                       the TCC command line. For example, the TCC command
                       line would look like this:

                          TCC  niftgraf graphics.lib  cga.obj  trip.obj
                       goth.obj

                    4. You register these files in your graphics program
                       like this:

 If you ever get a      /* Header file declares CGA_driver, triplex_font &
      linker error      gothic_font */
   Segment exceeds      #include <graphics.h>
 64K after linking
   in some drivers      /* Register and check for errors (one never
     and/or fonts,      knows...) */
      refer to the
following section.      if (registerbgidriver(CGA_driver) < 0) exit(1);
                        if (registerbgifont(triplex_font) < 0) exit(1);
                        if (registerbgifont(gothic_font)  < 0) exit(1);

                        /* ... */

                        initgraph(....);     /* initgraph should be called
                                                after registering */

                        /* ... */


     The /F option  =======================================================

                    This section explains what steps to take if you get the
                    linker error Segment exceeds 64K (or a similar error)
                    after linking in several driver and/or font files
                    (especially with small- and compact-model programs).

                    By default, the files created by BGIOBJ.EXE all use the
                    same segment (called _TEXT). This can cause problems if
                    your program links in many drivers and/or fonts, or
                    when you're using the small or compact memory model.

                    To solve this problem, you can convert one or more of
                    the drivers or fonts with the BGIOBJ /F option. This
                    option directs BGIOBJ to use a segment name of the form
                    filename_TEXT, so that the default segment is not
                    overburdened by all the linked-in drivers and fonts
                    (and, in small and compact model programs, all the pro-
                    gram code). For example, the following two BGIOBJ com-




                                   - 5 -






                    mand lines direct BGIOBJ to use segment names of the
                    form EGAVGA_TEXT and SANS_TEXT.

                     bgiobj  /F  egavga
                     bgiobj  /F  sans

                    When you select /F, BGIOBJ also appends F to the target
                    object file name (EGAVGAF.OBJ, SANSF.OBJ, and so
                    forth), and appends _far to the name that will be used
                    with registerfarbgidriver and registerfarbgifont. (For
                    example, EGAVGA_driver becomes EGAVGA_driver_far.)

                    For files created with /F, you must use these far
                    registering routines instead of the regular
                    registerbgidriver and registerbgifont. For example,

                     if (registerfarbgidriver(EGAVGA_driver_far) < 0)
                     exit(1);
                     if (registerfarbgifont(sansserif_font_far)  < 0)
                     exit(1);


 Advanced features  =======================================================

                    This section explains some of BGIOBJ's advanced
                    features, and the routines registerfarbgidriver and
                    registerfarbgifont. Only experienced users should use
                    these features.

                    This is the full syntax of the BGIOBJ.EXE command line:

                       BGIOBJ  [/F] source destination public-name seg-name
                    seg-class

                    This table describes each component of the BGIOBJ com-
                    mand line.


                    -------------------------------------------------------
                    Component              Description
                    -------------------------------------------------------


                    /F or -F               This option instructs BGIOBJ.EXE
                                           to use a segment name other than
                                           _TEXT (the default), and to
                                           change the public name and
                                           destination file name. (See page



                                   - 6 -






                                           5 for a detailed discussion of
                                           /F.)

                    source                 This is the driver or font file
                                           to be converted. If the file is
                                           not one of the driver/font files
                                           shipped with Turbo C++, you
                                           should specify a full file name
                                           (including extension).

                    destination            This is the name of the object
                                           file to be produced. The default
                                           destination file name is
                                           source.OBJ, or sourceF.OBJ if
                                           you use the /F option.

                    public-name            This is the name that will be
                                           used in the program in a call to
                                           registerbgidriver or
                                           registerbgifont (or their
                                           respective far versions) to link
                                           in the object module.

                                           The public name is the external
                                           name used by the linker, so it
                                           should be the name used in the
                                           program, prefixed with an
                                           underscore. If your program uses
                                           Pascal calling conventions, use
                                           only uppercase letters, and do
                                           not add an underscore.

                    seg-name               This is an optional segment
                                           name; the default is _TEXT (or
                                           filename_TEXT if /F is
                                           specified)

                    seg-class              This is an optional segment
                                           class; the default is CODE.

                    -------------------------------------------------------

                    All parameters except source are optional. However, if
                    you need to specify an optional parameter, all the
                    parameters preceding it must also be specified.






                                   - 7 -






                    If you choose to use your own public name(s), you have
                    to add declaration(s) to your program, using one of the
                    following forms:

                     void public_name(void);        /* if /F not used, */
                                                    /* default segment name
                     used */

                     extern int far public_name[];  /* if /F used, or */
                                                    /* segment name not
                     _TEXT */

                    In these declarations, public_name matches the public-
                    name you used when converting with BGIOBJ. The
                    graphics.h header file contains declarations of the
                    default driver and font public names; if you use those
                    default public names you don't have to declare them as
                    just described.

                    After these declarations, you have to register all the
                    drivers and fonts in your program. If you don't use the
                    /F option and don't change the default segment name,
                    you should register drivers and fonts through
                    registerbgidriver and registerbgifont; otherwise, use
                    registerfarbgidriver and registerfarbgifont.

                    Here is an example of a program that loads a font file
                    into memory:

                     /* Example of loading a font file into memory */

                     #include <graphics.h>
                     #include <io.h>
                     #include <fcntl.h>
                     #include <stdio.h>
                     #include <conio.h>
                     #include <stdlib.h>
                     #include <process.h>
                     #include <alloc.h>

                     main()
                     {
                        void    *gothic_fontp;    /* points to font buffer
                     in memory */
                        int      handle;          /* file handle used for
                     I/O */
                        unsigned fsize;           /* size of file (and
                     buffer) */



                                   - 8 -






                        int errorcode;
                        int graphdriver;
                        int graphmode;

                     /* open font file */
                        handle = open("GOTH.CHR", O_RDONLY|O_BINARY);
                        if (handle == -1)
                        {
                           printf("unable to open font file 'GOTH.CHR'\n");
                           exit(1);
                        }
                     /* find out size of the file */
                        fsize = filelength(handle);
                     /* allocate buffer */
                        gothic_fontp = malloc(fsize);
                        if (gothic_fontp == NULL)
                        {
                          printf("unable to allocate memory for font file
                     'GOTH.CHR'\n");
                          exit(1);
                        }
                     /* read font into memory */
                        if (read(handle, gothic_fontp, fsize) != fsize)
                        {
                           printf("unable to read font file 'GOTH.CHR'\n");
                           exit(1);
                        }
                     /* close font file */
                        close(handle);
                     /* register font */
                        if (registerfarbgifont(gothic_fontp) !=
                     GOTHIC_FONT)
                        {
                           printf("unable to register font file
                     'GOTH.CHR'\n");
                           exit(1);
                        }
                     /* detect and initialize graphix */
                        graphdriver = DETECT;
                        initgraph(&graphdriver, &graphmode, "..");
                        errorcode = graphresult();
                        if (errorcode != grOk)
                        {
                           printf("graphics error:
                     %s\n",grapherrormsg(errorcode));
                           exit(1);
                        }
                        settextjustify(CENTER_TEXT, CENTER_TEXT);



                                   - 9 -






                        settextstyle(GOTHIC_FONT, HORIZ_DIR, 4);
                        outtextxy(getmaxx()/2,getmaxy()/2,
                                  "Borland Graphics Interface (BGI)");
                     /* press a key to terminate */
                        getch();
                     /* shut down graphics system */
                        closegraph();
                        return(0);
                     }



===========================================================================
CPP: The preprocessor
===========================================================================

    CPP produces a  Often, when the compiler reports an error inside a
  list (in a file)  macro or an include file, you can get more information
     of a C source  about what the error is if you can see the include
  program in which  files or the results of the macro expansions. In many
 include files and  multi-pass compilers, a separate pass performs this
    #define macros  work, and the results of the pass can be examined.
         have been  Since Turbo C++ uses an integrated single-pass compil-
   expanded. It is  er, we provided CPP to supply the first-pass
    not needed for  functionality found in other compilers.
    normal compil-
       ations of C  You use CPP just as you would use TCC, the standalone
         programs.  compiler. CPP reads the same TURBOC.CFG file for
                    default options, and accepts the same command-line
                    options as TCC.

                    The TCC options that don't pertain to CPP are simply
                    ignored by CPP. To see the list of arguments handled by
                    CPP, type cpp at the DOS prompt. To see how those
                    arguments work, see Chapter 5 in the Programmer's
                    Guide.

                    With one exception, the file names listed on the CPP
                    command line are treated like they are in TCC, with
                    wildcards allowed. The exception to this is that all
                    files are treated as C source files. There is no
                    special treatment for .OBJ, .LIB, or .ASM files.

                    For each file processed by CPP, the output is written
                    to a file in the current directory (or the output
                    directory named by the -n option) with the same name as
                    the source name but with an extension of .I.




                                  - 10 -






                    This output file is a text file containing each line of
                    the source file and any include files. Any preproces-
                    sing directive lines have been removed, along with any
                    conditional text lines excluded from the compile.
                    Unless you use a command-line option to specify other-
                    wise, text lines are prefixed with the file name and
                    line number of the source or include file the line came
                    from. Within a text line, any macros are replaced with
                    their expansion text.

        Important!  The resulting output of CPP cannot be compiled because
                    of the file name and line number prefix attached to
                    each source line.


    CPP as a macro  =======================================================
      preprocessor
                    The -P option to CPP tells it to prefix each line with
                    the source file name and line number. If you give it -
                    P- (turning this option off), CPP omits this line
                    number information. With this option turned off, CPP
                    can be used as a macro preprocessor; the resulting .I
                    file can then be compiled with TC or TCC.


        An example  =======================================================

                    The following simple program illustrates how CPP
                    preprocesses a file, first with -P selected, then with
                    -P-.

                    Source file: HELLOAJ.C
                     #define NAME "H.R. Floyd"
                     #define BEGIN {
                     #define END   }

                     main()
                     BEGIN
                        printf("%s\n", NAME);
                     END

                    Command line used to invoke CPP as a preprocessor:
                       CPP HELLOAJ.C

                    Output:
                     HELLOAJ.c 1:
                     HELLOAJ.c 2:
                     HELLOAJ.c 3:



                                  - 11 -






                     HELLOAJ.c 4:
                     HELLOAJ.c 5: main()
                     HELLOAJ.c 6: {
                     HELLOAJ.c 7:    printf("%s\n","H.R. Floyd");
                     HELLOAJ.c 8: }

                    Command line used to invoke CPP as a macro
                    preprocessor:
                       CPP -P- HELLOAJ.C

                    Output:
                     main()
                     {
                        printf("%s\n","H.R. Floyd");
                     }



===========================================================================
GREP: A text-search utility
===========================================================================

                    GREP (Global Regular Expression Print) is a powerful
                    text-search program derived from the UNIX utility of
                    the same name. GREP searches for a text pattern in one
                    or more files or in its standard input stream.

                    Here's a quick example of a situation where you might
                    want to use GREP. Suppose you wanted to find out which
                    text files in your current directory contained the
                    string "Bob". You would issue the command

                       grep Bob *.txt

                    and GREP would respond with a list of the lines in each
                    file (if any) that contained the string "Bob". Because
                    the default for GREP is to ignore case, the strings
                    "bob" and "BoB" would also be considered matches. You
                    can use options to make your search case sensitive.

                    GREP can do a lot more than match a single, fixed
                    string. In the section that follows, you'll see how to
                    make GREP search for any string that matches a
                    particular pattern.







                                  - 12 -






      Command-line  =======================================================
            syntax
                    The general command-line syntax for GREP is

                      grep [options] searchstring [file(s) ... ]

                    options consist of one or more letters, preceded by a
                    hyphen (-), that let you change various aspects of
                    GREP's behavior.

                    searchstring gives the pattern to search for.

                    file(s) tells GREP which files to search. (If you don't
                    specify a file, GREP searches its standard input; this
                    lets you use GREP with pipes and redirection.) If you
                    find that the results of your GREP are longer than one
                    screen, you can redirect the output to a file. For
                    example, you could use this command

                       GREP "Bob" *.txt >gfile

                    which searches all files in the current directory that
                    end with .TXT, then places the results in a file called
                    GFILE. (You can name this file anything you like.)
                    Then, use your word processor (or Turbo C++'s editor)
                    to access GFILE to read the results of the search.

                    The command

                       GREP ?

                    prints a brief help screen showing GREP's command-line
                    options, special characters, and defaults. (See the
                    description of the -u command-line option for
                    information on how to change GREP's defaults.)


      GREP options  =======================================================

                    In the command line, options are one or more single
                    characters preceded by a hyphen (-). Each individual
                    character is a switch that you can turn on or off: A
                    plus symbol (+) after a character turns the option on;
                    a hyphen (-) after the character turns the option off.
                    The + sign is optional; for example, -r means the same
                    thing as -r+. You can list multiple options





                                  - 13 -






                    individually (like this: -i -d -l), or you can combine
                    them (like this: -ild or -il, -d, and so on); it's all
                    the same to GREP.

                    Here are the GREP option characters and their meanings:


------------------------------------------------------------------------------
Option Meaning
------------------------------------------------------------------------------


  -c   Match Count only: Prints only a count of matching lines. For each file
       that contains at least one matching line, GREP prints the file name and
       a count of the number of matching lines. Matching lines are not
       printed. This option is off by default.

  -d   Search subdirectories: For each file specified on the command line,
       GREP searches for all files that match the file specification, both in
       the directory specified and in all subdirectories below the specified
       directory. If you give a file without a path, GREP assumes the files
       are in the current directory. This option is off by default.

  -i   Ignore case: GREP ignores upper/lowercase differences (case folding).
       When this option is on, GREP treats all letters a to z as identical to
       the corresponding letters A to Z in all situations. This option is on
       by default.

  -l   List file names only: Prints only the name of each file containing a
       match. After GREP finds a match, it prints the file name and processing
       immediately moves on to the next file. This option is off by default.

  -n   Line Numbers: Each matching line that GREP prints is preceded by its
       line number. This option is off by default.

  -o   UNIX output format: Changes the output format of matching lines to
       support more easily the UNIX style of command-line piping. All lines of
       output are preceded by the name of the file that contained the matching
       line. This option is off by default.

  -r   Regular expression search: The text defined by searchstring is treated
       as a regular expression instead of as a literal string. This option is
       on by default. This option is on by default.

       A regular expression is one or more occurrences of one or more
       characters optionally enclosed in quotes. The following symbols are
       treated specially:
        ^  start of line       $  end of line



                                  - 14 -






        .  any character       \  quote next character
        *  match zero or more  +  match one or more

        [aeiou0-9]             match a, e, i, o, u, and 0 thru 9
        [^aeiou0-9]            match anything but a, e, i, o, u, and 0 thru 9

  -u   Update options: GREP will combine the options given on the command line
       with its default options and write these to the GREP.COM file as the
       new defaults. (In other words, GREP is self-configuring.) This option
       allows you to tailor the default option settings to your own taste. If
       you want to see what the defaults are in a particular copy of GREP.COM,
       type

        GREP ?

       at the DOS prompt. Each option on the help screen will be followed by a
       + or a - depending on its default setting. This option is off by
       default.

  -v   Nonmatch: Prints only nonmatching lines. Only lines that do not contain
       the search string are considered to be nonmatching lines. This option
       is off by default.

  -w   Word search: Text found that matches the regular expression is
       considered a match only if the character immediately preceding and
       following cannot be part of a word. The default word character set
       includes A to Z, 0 to 9, and the underscore ( _ ). This option is off
       by default.

       An alternate form of this option lets you specify the set of legal word
       characters. Its form is -w[set], where set is any valid regular
       expression set definition.

       If you define the set with alphabetic characters, it is automatically
       defined to contain both the uppercase and lowercase values for each
       letter in the set (regardless of how it is typed), even if the search
       is case-sensitive. If you use the -w option in combination with the -u
       option, the new set of legal characters is saved as the default set.

  -z   Verbose: GREP prints the file name of every file searched. Each
       matching line is preceded by its line number. A count of matching lines
       in each file is given, even if the count is zero. This option is off by
       default.

------------------------------------------------------------------------------






                                  - 15 -






------------------  Remember that each of GREP's options is a switch: Its
          Order of  state reflects the way you last set it. At any given time,
        precedence  each option can only be on or off. Each occurrence of a
------------------  given option on the command line overrides its previous
                    definition. Given this command line,

                       grep -r -i - -d -i -r -  main( my*.c

                    GREP runs with the -d option on, the -i option on, and the
                    -r option off.

                    You can install your preferred default setting for each
                    option in GREP.COM with the -u option. For example, if you
                    want GREP to always do a verbose search (-z on), you can
                    install it with the following command:

                       grep -u -z


 The search string  ==========================================================

                    To use GREP well, you'll need to become proficient at
                    writing search strings. The value of searchstring defines
                    the pattern GREP searches for. A search string can be
                    either a regular expression or a literal string.

                    o In a regular expression, certain characters have special
                      meanings: They are operators that govern the search.

                    o In a literal string, there are no operators: Each
                      character is treated literally.

                    You can enclose the search string in quotation marks to
                    prevent spaces and tabs from being treated as delimiters.
                    The text matched by the search string cannot cross line
                    boundaries; that is, all the text necessary to match the
                    pattern must be on a single line.

                    A regular expression is either a single character or a set
                    of characters enclosed in brackets. A concatenation of
                    regular expressions is a regular expression.










                                  - 16 -






------------------  When you use the -r option (on by default), the search
      Operators in  string is treated as a regular expression (not a literal
           regular  expression). The following characters take on special
       expressions  meanings:
------------------

---------------------------------------------------------------------------
Option Meaning
---------------------------------------------------------------------------


  ^    A circumflex at the start of the expression matches the start of a
       line.

  $    A dollar sign at the end of the expression matches the end of a
       line.

  .    A period matches any character.

  *    An expression followed by an asterisk wildcard matches zero or more
       occurrences of that expression. For example, in to*, the * operates
       on the expression o; it matches t, to, too, etc. (t followed by zero
       or more os), but doesn't match ta.

  +    An expression followed by a plus sign matches one or more
       occurrences of that expression: to+ matches to, too, etc., but not
       t.

  [ ]  A string enclosed in brackets matches any character in that string,
       but no others. If the first character in the string is a circumflex
       (^), the expression matches any character except the characters in
       the string.

       For example, [xyz] matches x, y, or z, while [^xyz] matches a and b,
       but not x, y, or z. You can specify a range of characters with two
       characters separated by a hyphen (-). These can be combined to form
       expressions (like [a-bd-z?], which matches the ? character and any
       lowercase letter except c).

  \    The backslash escape character tells GREP to search for the literal
       character that follows it. For example, \. matches a period instead
       of "any character." The backslash can be used to quote itself; that
       is, you can use \\ to indicate a literal backslash character in a
       GREP expression.

---------------------------------------------------------------------------





                                  - 17 -






              Note  Four of the "special" characters ($, ., *, and +) don't
                    have any special meaning when used within a bracketed
                    set. In addition, the character ^ is only treated
                    specially if it immediately follows the beginning of
                    the set definition (immediately after the [ delimiter).

                    Any ordinary character not mentioned in the preceding
                    list matches that character. For example, the greater
                    than sign, >, matches the greater than sign (>), #
                    matches #, and so on.


              File  =======================================================
    specifications
                    file(s) tells GREP which files (or groups of files) to
                    search. file(s) can be an explicit file name, or a
                    "generic" file name incorporating the DOS ? and *
                    wildcards. In addition, you can enter a path (drive and
                    directory information) as part of file(s). If you give
                    file(s) without a path, GREP searches the current
                    directory.

                    If you don't specify any files, input to GREP must come
                    from redirection (<) or a vertical bar (|).


Some GREP examples  =======================================================

                    The following examples show how to combine GREP's
                    features to do different kinds of searches. They assume
                    GREP's default settings are unchanged.


------------------  The search string here tells GREP to search for the
         Example 1  word main with no preceding lowercase letters ([^a-z]),
------------------  followed by zero or more occurrences of blank spaces
                    (\ *), then a left parenthesis.

                    Since spaces and tabs are normally considered to be
                    command-line delimiters, you must quote them if you
                    want to include them as part of a regular expression.
                    In this case, the space after main is quoted with the
                    backslash escape character. You could also accomplish
                    this by placing the space in double quotes.

                    Command line:
                                grep -r [^a-z]main\ *( *.c




                                  - 18 -






                    Matches:    main(i:integer)
                                main(i,j:integer)
                                if (main  ()) halt;
                                if (MAIN  ()) halt;

                    Does not match:
                                mymain()

                    Files searched:
                                *.C in current directory.


------------------  Because the backslash (\) and period (.) characters
         Example 2  usually have special meaning in path and file names,
------------------  you must place the backslash escape character immedi-
                    ately in front of them if you want to search for them.
                    The -i option is used here, so the search is not case
                    sensitive.

                    Command line:
                                grep -ri [a-c]:\\data\.fil *.c *.inc

                    Matches:    A:\data.fil
                                c:\Data.Fil
                                B:\DATA.FIL

                    Does not match:
                                d:\data.fil
                                a:data.fil

                    Files searched:
                                *.C and *.INC in current directory.


------------------  This format defines how to search for a given word.
         Example 3
------------------  Command line:
                                grep -ri [^a-z]word[^a-z] *.doc

                    Matches:    every new word must be on a new line.
                                MY WORD!
                                word--smallest unit of speech.
                                In the beginning there was the WORD, and
                                the WORD

                    Does not match:
                                Each file has at least 2000 words.
                                He misspells toward as toword.



                                  - 19 -






                    Files searched:
                                *.DOC in the current directory.


------------------  This format defines another, even more basic single-
         Example 4  word search.
------------------
                    Command line:
                                grep -iw word *.doc

                    Matches:    every new word must be on a new line
                                However,
                                MY WORD!
                                word: smallest unit of speech which conveys
                                In the beginning there was the WORD, and

                    Does not match:
                                each document contains at least 2000 words!
                                He seems to continually misspell "toward"
                                as "toword."

                    Files searched:
                                *.DOC in the current directory.


------------------  This is an example of how to search for a string with
         Example 5  embedded spaces.
------------------
                    Command line:
                                grep "search string with spaces" *.doc *.c
                                a:\work\myfile.*

                    Matches:    This is a search string with spaces in it.

                    Does not match:
                                This search string has spaces in it.

                    Files searched:
                                *.DOC and *.C in the current directory, and
                                MYFILE.* in a directory called \WORK on
                                drive A.










                                  - 20 -






------------------  This example searches for any one of the characters
         Example 6  " . : ? ' and , at the end of a line.
------------------
                    The double quote within the range is preceded by an
                    escape character so it is treated as a normal character
                    instead of as the ending quote for the string. Also,
                    the $ character appears outside of the quoted string.
                    This demonstrates how regular expressions can be
                    concatenated to form a longer expression.

                    Command line:
                                grep -rd "[ ,.:?'\"]"$ \*.doc

                    Matches:    He said hi to me.
                                Where are you going?
                                In anticipation of a unique situation,
                                Examples include the following:
                                "Many men smoke, but fu man chu."

                    Does not match:
                                He said "Hi" to me
                                Where are you going? I'm headed to the

                    Files searched:
                                *.DOC in the root directory and all its
                                subdirectories on the current drive.


------------------  This example ignores case and just prints the names of
         Example 7  any files that contain at least one match. The three
------------------  command-line examples show different ways of specifying
                    multiple options.

                    Command line:
                                grep -ild " the " \*.doc
                                or
                                grep -i -l -d " the " \*.doc
                                or
                                grep -il -d " the " \*.doc

                    Matches:    Anyway, this is the time we have
                                do you think? The main reason we are

                    Does not match:
                                He said "Hi" to me just when I
                                Where are you going? I'll bet you're headed





                                  - 21 -






                    Files searched:
                                *.DOC in the root directory and all its
                                subdirectories on the current drive.


------------------  This example redefines the current set of legal
         Example 8  characters for a word as the assignment operator (=)
------------------  only, then does a word search. It matches C assignment
                    statements, which use a single equal sign (=), but not
                    equality tests, which use a double equal sign (==).

                    Command line:
                                grep -w[=] = *.c

                    Matches:    i = 5;
                                j=5;
                                i += j;

                    Does not match:
                                if (i == t) j++;
                                /* ======================= */

                    Files searched:
                                *.C in the current directory.



===========================================================================
OBJXREF: The object module cross-reference utility
===========================================================================

                    OBJXREF examines a list of object files and library
                    files and produces reports on their contents. One type
                    of report lists definitions of public names and
                    references to them. The other type lists the segment
                    sizes defined by object modules.

                    There are two categories of public names: global
                    variables and function names. The TEST1.C and TEST2.C
                    files in the section "Sample OBJXREF reports" (page 28)
                    illustrate definitions of public names and external
                    references to them.

                    Object modules are object (.OBJ) files produced by TC,
                    TCC or TASM. A library (.LIB) file contains multiple
                    object modules. An object module generated by TC is
                    given the same name as the .C source file it was com-
                    piled from. This is also true for TCC, unless a



                                  - 22 -






                    different output file name is specifically indicated
                    with the -o TCC command-line option.


  The OBJXREF com-  =======================================================
         mand line
                    The OBJXREF command line consists of the word OBJXREF
                    followed by a series of command-line options and a list
                    of object and library file names, separated by a space
                    or tab character. The syntax is as follows:

                      OBJXREF  options   filename  filename ...

                    The command-line options determine the kind of reports
                    that OBJXREF will generate and the amount of detail
                    that OBJXREF will provide. They are discussed in more
                    detail in the next section.

                    Each option begins with a forward slash (/) followed by
                    a one- or two-character option name.

                    Object files and library files may be specified either
                    on the command line or in a response file. On the com-
                    mand line, file names are separated by a space or a
                    tab. All object modules specified as .OBJ files are
                    included in reports. Like TLINK, however, OBJXREF
                    includes only those modules from .LIB files which
                    contain a public name referenced by an .OBJ file or by
                    a previously included module from a .LIB file.

                    As a general rule, you should list all the .OBJ and
                    .LIB files that are needed if the program is to link
                    correctly, including the startup .OBJ file and one or
                    more C libraries.

                    File names may include a drive and directory path. The
                    DOS ? and * wildcard characters may be used to identify
                    more than one file. File names may refer to .OBJ object
                    files or to .LIB library files. (If you don't give a
                    file extension, the .OBJ extension is assumed.)

                    Options and file names may occur in any order in the
                    command line.

                    OBJXREF reports are written to the DOS standard output.
                    The default is the screen. The reports can be sent to a
                    printer (as with >LPT1:) or to a file (as with
                    >lstfile) with the DOS redirection character (>).



                                  - 23 -






                    Entering OBJXREF with no file names or options produces
                    a summary of available options.


------------------  OBJXREF command-line options fall into two categories:
       The OBJXREF  control options and report options.
      command-line
           options
------------------  Control options
                    =======================================================

                    Control options modify the default behavior of OBJXREF
                    (the default is that none of these options are
                    enabled).


                    -------------------------------------------------------
                    Option Meaning
                    -------------------------------------------------------


                      /I   Ignore case differences in public names. Use
                           this option if you use TLINK without the /C
                           option (which makes case differences
                           significant).

                      /D   Look for .OBJ files in another directory. If you
                           want OBJXREF to look for .OBJ files in a
                           directory other than the current one, include
                           the directory name on the command line, prefixed
                           with /D:

                              OBJXREF /Ddir1 [; dir2 [; dir3]]

                           or

                              OBJXREF /Ddir1 [/Ddir2] [/Ddir3]

                           OBJXREF will search each of the directories in
                           the specified order for all object and library
                           files.

        Important!         If you don't use a /D option, OBJXREF will
                           search only the current directory. If you do use
                           a /D option, however, the current directory will
                           not be searched unless it is included in the
                           directory list. For example, if you wanted
                           OBJXREF to search first the BORLAND directory



                                  - 24 -






                           and then the current directory for files, you
                           would enter

                              OBJXREF /Dborland;.

                           The period denotes the current directory.


                      /F   Include full library. All object modules in
                           specified .LIB files are included even if they
                           do not contain public names that are referenced
                           by an object module being processed by OBJXREF.
                           This provides information on the entire contents
                           of a library file. (See example 4 in the section
                           "OBJXREF examples.")

                      /O   Allows you to specify an output file where
                           OBJXREF will send any reports generated. Its
                           syntax is as follows:

                              OBJXREF filename.obj /report option
                              /Ooutputfilename.ext

                           By default all output is sent to the screen.


                      /V   Verbose output. Lists names of files read and
                           displays totals of public names, modules,
                           segments, and classes.

                      /Z   Include zero-length segment definitions. Object
                           modules may define a segment without allocating
                           any space in it. Listing these zero length
                           segment definitions normally makes the module
                           size reports harder to use but it can be
                           valuable if you are trying to remove all defini-
                           tions of a segment.

                    -------------------------------------------------------


                    Report options
                    =======================================================

                    Report options govern what sort of report is generated,
                    and the amount of detail that OBJXREF provides.





                                  - 25 -






                    -------------------------------------------------------
                    Option Report generated
                    --------------------------------------------------


                      /RC  Report by class type: Module sizes ordered
                           by class type of segment.

                      /RM  Report by module: Public names ordered by
                           defining module.

                      /RP  Report by public names: Public names in
                           order with defining module name.

       This is the    /RR  Report by reference: Public name defini-
          default.         tions and references ordered by name.

                      /RS  Report of module sizes: Module sizes
                           ordered by segment name.

                      /RU  Report of unreferenced symbol names:
                           Unreferenced public names ordered by
                           defining module.

                      /RV  Verbose reporting: OBJXREF produces a
                           report of every type.

                      /RX  Report by external reference: External
                           references ordered by referencing module
                           name.

                    --------------------------------------------------

                    Public names defined in .C files appear in reports with
                    a leading underscore in the reports unless the -U-
                    option was specified when the file was compiled (main
                    appears as _main).

                    You can limit the modules, segments, classes, or public
                    names that OBJXREF reports on by entering the
                    appropriate name on the command line prefixed with the
                    /N option. For example,

                       OBJXREF filelist /RM /NC0

                    tells OBJXREF to generate a report listing information
                    only for the module named C0.




                                  - 26 -






    Response files  =======================================================

                    The command line is limited by DOS to a maximum of 128
                    characters. If your list of options and file names will
                    exceed this limit, you must place your file names in a
                    response file.

                    A response file is a text file that you make with a
                    text editor. Since you may already have prepared a list
                    of the files that make up your program for other Turbo
                    C++ programs, OBJXREF recognizes several response file
                    types.

                    Response files are called from the command line using
                    one of the following options. The response file name
                    must follow the option without an intervening space
                    (so, for example, you would type /Lresp, not /L resp).

                    You can specify more than one response file on the com-
                    mand line; additional .OBJ and .LIB file names can
                    precede or follow them.


------------------  You can create a free-form response file with a text
Free-form response  editor. Just list the names of all .OBJ and .LIB files
             files  needed to make your .EXE file.
------------------
     Any file name  To use free-form files with OBJXREF, type in each
     listed in the  response file name on the command line, preceded by an
     response file  @, and separate it from other command-line entries with
 without an exten-  a space or tab:
sion is assumed to
  be an .OBJ file.     @filename  @filename ...


------------------  You can also use project files of the type generated by
     Project files  Turbo C++'s integrated environment as response files.
------------------  In the command line, precede the project file name with
                    /P, like this:

                       /Pfilename

                    If the file name does not include an explicit exten-
                    sion, a .PRJ extension is assumed.







                                  - 27 -






                    File names in the project file with a .C extension or
                    no extension are interpreted as specifying the
                    corresponding .OBJ file. You need not remove file
                    dependencies specified inside parentheses; they are
                    ignored by OBJXREF.

              Note  By itself, the list of files in a .PRJ file does not
                    specify a complete program--you must also specify a
                    startup file (C0x.OBJ) and one or more Turbo C++
                    library files (MATHX.LIB, EMU.LIB, and CX.LIB, for
                    example). In addition, you may need to use the /D
                    option to specify the directory where OBJXREF should
                    look for your .OBJ files.


------------------  Files in TLINK response-file format can also be used by
   Linker response  OBJXREF. A linker response file called from the command
             files  line is preceded by /L, like so:
------------------
                       /Lfilename

                    To see how to use one of these files, refer to Example
                    2 in the section "Examples of how to use OBJXREF."


    Sample OBJXREF  =======================================================
           reports
                    Suppose you have two source files in your Turbo C++
                    directory, and want to generate OBJXREF reports on the
                    object files compiled from them. The source files are
                    called TEST1.C and TEST2.C, and they look like this:

                     /* test1.c */
                     int i1;                       /* defines i1 */
                     extern int i2;                /* refers to i2 */
                     static int i3;                /* not a public name */
                     extern void look(void);       /* refers to look */

                     void main(void)               /* defines main */
                     {
                        int i4;                    /* not a public name */

                        look();                    /* refers to look */
                     }

                     /* test2.c */
                     #include <process.h>
                     extern int i1;                /* refers to i1 */



                                  - 28 -






                     int i2;                       /* defines i2 */

                     void look(void)               /* defines look */
                     {
                        exit(i1);                  /* refers to exit... */
                     }                             /* and to i1 */

                    The object modules compiled from these source files are
                    TEST1.OBJ and TEST2.OBJ. You can tell OBJXREF what kind
                    of report to generate about these .OBJ files by
                    entering the file names on the command line, followed
                    by a /R and a second letter denoting report type.

              Note  The following examples show only useful parts of the
                    output.


------------------  A report by public names lists each of the public names
  Report by public  defined in the object modules being reported on,
       names (/RP)  followed by the name of the module in which it is
------------------  defined.

                    If you enter this on the command line:

                       OBJXREF  /RP  test1  test2

                    OBJXREF generates a report that looks like this:

                     SYMBOL           DEFINED IN
                     _i1              TEST1
                     _i2              TEST2
                     _look            TEST2
                     _main            TEST1


------------------  A report by module lists each object module being
  Report by module  reported on, followed by a list of the public names
             (/RM)  defined in it.
------------------
                    If you enter this on the command line:

                       OBJXREF  /RM  test1  test2

                    OBJXREF generates a report that looks like this:

                     MODULE: TEST1 defines the following symbols:
                             public: _i1
                             public: _main



                                  - 29 -






                     MODULE: TEST2 defines the following symbols:
                             public: _i2
                             public: _look


------------------  A report by reference lists each public name with the
         Report by  defining module in parentheses on the same line.
   reference (/RR)  Modules that refer to this public name are listed on
------------------  following lines indented from the left margin.

       This is the  If you enter this on the command line:
     default if no
  report option is     OBJXREF  /RR  C0  test1  test2  CS.LIB
        specified.
                    OBJXREF generates a report that looks like this:

                     _exit (EXIT)
                          C0
                          TEST2
                     _i1 (TEST1)
                          TEST2
                     _i2 (TEST2)
                     _look (TEST2)
                          TEST1
                     _main (TEST1)
                          C0


------------------  A report by external references lists each module
Report by external  followed by a list of external references it contains.
  references (/RX)
------------------  If you enter this on the command line:

                       OBJXREF  /RX  C0  test1  test2  CS.LIB

                    OBJXREF generates a report that looks like this:

                     MODULE: C0 references the following symbols:
                             _main
                     MODULE: TEST1 references the following symbols:
                             _i2
                             _look
                     MODULE: TEST2 references the following symbols:
                             _exit
                             _i1






                                  - 30 -






------------------  A report by sizes lists segment names followed by a
  Report of module  list of modules that define the segment. Sizes in bytes
       sizes (/RS)  are given in decimal and hexadecimal notation. The word
------------------  uninitialized appears where no initial values are
                    assigned to any of the symbols defined in the segment.
                    Segments defined at absolute addresses in a .ASM file
                    are flagged Abs to the left of the segment size.

                    If you enter this on the command line:

                       OBJXREF  /RS  test1  test2

                    OBJXREF generates a report that looks like this:

  These files were   TEST1_TEXT
compiled using the           6 (00006h)   TEST1
      large memory           6 (00006h)   total
            model.   TEST2_TEXT
                            10 (0000Ah)   TEST2
                            10 (0000Ah)   total
                     _BSS
                             4 (00004h)   TEST1, uninitialized
                             2 (00002h)   TEST2, uninitialized
                             6 (00006h)   total


------------------  A report by class type lists segment size definitions
   Report by class  by segment class. The CODE class contains instructions,
        type (/RC)  DATA class contains initialized data and BSS class
------------------  contains uninitialized data. Segments which do not have
                    a class type will be listed under the notation No class
                    type.

                    If you enter this on the command line:

                       OBJXREF  /RC  C0  test1  test2  CS.LIB

                    OBJXREF generates a report that looks like this:

                     BSS
                            4  (00004h)   TEST1
                            2  (00002h)   TEST2
                          ...
                          132  (00084h)   total
                     CODE
                            6  (00006h)   TEST1





                                  - 31 -






                           10  (0000Ah)   TEST2
                           16  (00010h)   total
                     DATA
                          143  (0008Fh)   C0
                          143  (0008Fh)   total


------------------  A report of unreferenced symbol names lists modules
         Report of  that define public names not referenced in other
      unreferenced  modules. Such a symbol is either:
symbol names (/RU)
------------------  o referenced only from within the defining module and
                      does not need to be defined as a public symbol (in
                      that case, if the module is in C, the keyword static
                      should be added to the definition; if the module is
                      in TASM, just remove the public definition).

                    o never used (therefore, it can be deleted to save code
                      or data space).

                    If you enter this on the command line:

                       OBJXREF  /RU  test1  test2

                    OBJXREF generates a report that looks like this:

                       MODULE: TEST2 defines the unreferenced symbol _i2.


------------------  If you enter /RV on the command line, OBJXREF generates
 Verbose reporting  one report of each type.
             (/RV)
------------------
Examples of how to  =======================================================
       use OBJXREF
                    These examples assume that the application files are in
                    the current directory of the default drive and that the
                    Turbo C++ startup files (C0x.OBJ) and the library files
                    are in the \TC\LIB directory.


------------------  C>OBJXREF \TC\lib\c0l test1 test2 \TC\lib\cl.lib
         Example 1
------------------  In this example, the TEST1.OBJ and TEST2.OBJ files and
                    the Turbo C++ startup file \TC\LIB\C0L.OBJ and the
                    library file \TC\LIB\CL.LIB are specified. Since no
                    report type is specified, the resulting report is the




                                  - 32 -






                    default report by reference, listing public names and
                    the modules that reference them.


------------------  C>OBJXREF /RV /Ltest1.arf
         Example 2
------------------  The TLINK response file TEST1.ARF contains the same
                    list of files as the command line in Example 1. The /RV
                    option is specified, so a report of every type will be
                    generated. TEST1.ARF contains

                     \TC\lib\c0l
                     test1 test2
                     test1.exe
                     test1.map
                     \TC\lib\cl


------------------  C>OBJXREF /RC B:c0s /Ptest1 @libs
         Example 3
------------------  The Turbo C++ project file TEST1.PRJ specifies
                    TEST1.OBJ and TEST2.OBJ. The response file @libs
                    specifies libraries on a disk in the B drive. TEST1.PRJ
                    contains

                     test1
                     test2.c

                    The file LIBS contains

                       b:maths.lib b:emu.lib b:cs.lib

                    The startup and library files specified depend on the
                    memory model and floating point options used in compil-
                    ation. The /RC causes a report of class type to be
                    output.


------------------  C>OBJXREF /F /RV \TC\lib\cs.lib
         Example 4
------------------  This example reports on all the modules in the Turbo
                    C++ library file CS.LIB; OBJXREF can produce useful
                    reports even when the files specified do not make a
                    complete program. The /F causes all modules in CS.LIB
                    file to be included in the report.






                                  - 33 -






     OBJXREF error  =======================================================
      messages and
          warnings  OBJXREF generates two sorts of diagnostic messages:
                    error messages and warnings.


------------------  Out of memory
    Error messages  OBJXREF performs its cross referencing in RAM memory
------------------  and may run out of memory even if TLINK is able to link
                    the same list of files successfully. When this happens,
                    OBJXREF aborts. Remove memory resident programs to get
                    more space, or add more RAM.


------------------  WARNING: Unable to open input file <filename>
          Warnings  The input file filename could not be located or opened.
------------------  OBJXREF proceeds to the next file.

                    WARNING: Unknown option - <option>
                    The option name option is not recognized by OBJXREF.
                    OBJXREF ignores the option.

                    WARNING: Unresolved symbol <symbol> in module <module>
                    The public name symbol referenced in module module is
                    not defined in any of the .OBJ or .LIB files specified.
                    OBJXREF flags the symbol in any reports it generates as
                    being referenced but not defined.

                    WARNING: Invalid file specification <filename>
                    Some part of the file name filename is invalid. OBJXREF
                    proceeds to the next file.

                    WARNING: No files matching <filename>
                    The file named filename listed on the command line or
                    in a response file could not be located or opened.
                    OBJXREF skips to the next file.

                    WARNING: Symbol <symbol> defined in <module1>
                    duplicated in <module2>
                    Public name symbol is defined in modules module1 and
                    module2. OBJXREF ignores the second definition.










                                  - 34 -






===========================================================================
PRJCFG: Configuration file utility
===========================================================================


                    Creates the command-line configuration file from a
                    project file. You can also use it to create or update a
                    project file from a configuration file.

                    The command-line compiler looks for a default
                    configuration file named TURBOC.CFG, but you can
                    specify a different file with +pathname option. To use
                    PRJCFG to create a TCC configuration file from a
                    project file, you would type the following:

                     PRJCFG ProjFile.PRJ ConfigFile.CFG

                    To make a project file from a configuration file, type

                     PRJCFG ConfigFile.CFG ProjFile.PRJ



===========================================================================
PRJCNVT: Old projects for new
===========================================================================

                    This utility converts Turbo C 1.0, 1.5, and 2.0 project
                    files to Turbo C++ project files. The syntax for it is

                       PRJCNVT infile[.PRJ] [outfile[.PRJ]]

                    or

                       PRJCNVT infile[.TC]  [outfile[.PRJ]]

                    If you specify a configuration file as input, it must
                    have a project file defined. The compiler options in
                    the .CFG file and the dependencies in the Turbo C 2.0
                    .PRJ file will be placed into the corresponding Turbo
                    C++ .PRJ file.

                    If you specify a project file as input, only
                    dependencies information will be placed into the Turbo
                    C++ .PRJ file. All compiler options will remain
                    default.





                                  - 35 -






                    If you don't provide an extension, .TC is assumed. If
                    PRJCVNT can't find a .TC file, it looks for a .PRJ
                    file.

                    The default name of the output file is the base name of
                    the input file with the extension .PRJ. For example,
                    STARS.TC will turn into STARS.PRJ. If the input and the
                    output name are the same, the old file will be renamed
                    to a .BAK file.



===========================================================================
PRJ2MAK: From project file to MAKE file
===========================================================================

                    This utility converts a .PRJ file to a .MAK file
                    (containing all relevant switches and settings) for use
                    with the MAKE utility. These files can be re-used
                    without accessing the IDE. The syntax for PRJ2MAK is

                       PRJ2MAK projectfile[.PRJ] [makefile[.MAK]
                    [config[.CFG]]]

                    The extension for the project file name is assumed to
                    be .PRJ unless you specify otherwise.

                    The default name for the new MAKE file is the base file
                    name of the .PRJ file with the extension .MAK. The
                    default name for the new .CFG file is the base file
                    name of the .MAK file with the extension .CFG.

                    To change the names of the makefile and configuration
                    files, just specify different names on the command
                    line.

                    Examples of valid execution:

                       PRJ2MAK MYPROJ.PRJ MAKEFILE.MAK TURBOC.CFG

                    This execution creates a makefile called MAKEFILE.MAK
                    with a configuration file called TURBOC.CFG.
                       PRJ2MAK MYPROJ.PRJ MAKEFILE.MAK

                    This execution creates a makefile called MAKEFILE.MAK
                    with a configuration file called MYPROJ.CFG.

                       PRJ2MAK MYPROJ



                                  - 36 -






                    This execution creates a makefile called MYPROJ.MAK and
                    a configuration file called MYPROJ.CFG.

                    The makefile that PRJ2MAK creates will set up a
                    redirection file for the linker response file and for
                    the .CFG file. They will be created when you run the
                    makefile that was generated. The linker response file
                    is a temporary file and will be deleted. The .CFG file
                    will be left as a file on disk.

                    PRJ2MAK places options that meet the following
                    requirements into the .CFG file: Those that are not
                    default to the Turbo C++ command-line compiler and have
                    been selected in the project file.

                    PRJ2MAK will use the library search path as a command
                    link option to TLINK, so that TLINK can search that
                    path for the startup module and for libraries.



===========================================================================
THELP: The Turbo Help utility
===========================================================================

                    THELP.COM is a RAM-resident (TSR) utility that accesses
                    Turbo C++'s online Help information for you when you
                    aren't using the IDE (that is, if you are using an
                    editor other than the one in the IDE, or you are using
                    the command-line version of Turbo C++, or if you are
                    using another product, such as Turbo Debugger). THELP
                    requires about 21K bytes of memory.


       Loading and  =======================================================
    invoking THELP















                                  - 37 -






                    You need to first load THELP in order to use it from
                    within another program (or from the command line). Make
                    sure that TCHELP.TCH, the text file containing the
                    Turbo C++ online help information, is in the current
   Warning! If you  directory. (If you want to keep TCHELP.TCH in another
 are going to have  directory, THELP has a special /F command-line option
 THELP resident in  that will enable THELP to find it; the INSTALL program
memory at the same  inserts the correct path information into THELP.)
  time as SideKick
   1.x or SideKick  To load THELP, just type
   Plus, make sure
    you load THELP     THELP [options]
   before you load
         SideKick.  at the DOS command line before you go into your
                    application. This needs to be done only once, when you
                    first boot up.

                    Once you are in the other application, you can activate
                    THELP at any time. Just position the cursor under the
                    item you want information on, then press the THELP hot
                    key. The default hot key is 5 on the numeric keypad
                    (scan code 4ch, shift state 00h).


  Navigating THELP  =======================================================

                    Use the following keys to navigate through the Help
                    screens that THELP displays on your monitor:


----------------------------------------------------------------------
Key      What it does
----------------------------------------------------------------------


Up  Down  Left  Right
         Move the highlight from keyword to keyword within the current
         Help screen.

Shift-Arrow
         Moves the cursor while marking a block.

Home and End
         Move to the beginning and end of a line, respectively.

Tab and Shift-Tab
         Moves to the next or previous keyword.




                                  - 38 -






PgUp/PgDn
         Moves from screen to screen if additional screens are
         available.

Enter    Selects a Help entry for the item highlighted in the current
         Help screen.

Esc      Ends Help session.

F1       Displays the Help Table of Contents screen.

Shift-F1 Displays the Help Index. You can search for a specific
         keyword incrementally. For example, you can find printf by
         typing p r i. With each letter you type, the list jumps to
         the keyword that starts with p, then to pr, then to pri, and
         so on.

Alt-F1   Pressing Alt-F1 repeatedly takes you in reverse order through
         the last 20 screens you have reviewed.

Alt-F    Selects a new Help file (if you have specified more than one
         help file in the THELP.CFG file or on the
         command-line).

Ctrl-P   Pastes the marked block or example text into your current
         application.

----------------------------------------------------------------------


     THELP options  =======================================================

                    Here is a summary of the THELP command-line options. If
                    you use more than one option, you must separate them
                    with spaces.

                    The command-line options can be placed in a
                    configuration file (called THELP.CFG) for convenience.
                    THELP.CFG must be located in the same directory as
                    THELP.COM and each option in the configuration file
                    must be placed on its own line and begin in the
                    leftmost column.


                    -------------------------------------------------------
Summary of THELP     Option       Specifies
    command-line    -------------------------------------------------------
         options



                                  - 39 -






                    Table 1.1: Summary of THELP command-line options
                    (continued)____________________________________________

                     /C#xx        Select color:
                                     # = color number
                                     xx = hex color values

                     /Fname       Full path and file name of Help file

                     /H, /?, ?    Display help screen

                     /Kxxyy       Change hot key:
                                     xx = shift state (hex)
                                     yy = scan code (hex)

                     /U           Remove THELP from memory

                     /Wx,y,w,h    Sets the window size and location.

                    -------------------------------------------------------


------------------  This option lets you customize the background and
     /C#xx (select  foreground colors of various elements in a help screen.
            color)  The /C option is followed by the number of the color
------------------  you want and the hex color values for background and
                    foreground, respectively.

                    There are twelve possible colors, numbered as follows:


                    -------------------------------------------------------
                      Number Element
                    -------------------------------------------------------


                       0     <USER> <GROUP> attribute
                       1     Monochrome border attribute
                       2     Color text attribute
                       3     Monochrome text attribute
                       4     Color keyword attribute
                       5     Monochrome keyword attribute
                       6     Color selected keyword word attribute
                       7     Monochrome selected keyword word attribute
                       8     Color example text attribute
                       9     Monochrome example text attribute
                       A     Color marked block attribute
                       B     Monochrome marked block attribute



                                  - 40 -






                    -------------------------------------------------------

                    The color values for a standard IBM-compatible color
                    display are as follows:


                    -------------------------------------------------------
                     First digit (background)   Second digit (foreground)
                    -------------------------------------------------------


                          0   <USER>              <GROUP> Black
                          1   Blue               1 Blue
                          2   Green              2 Green
                          3   Cyan               3 Cyan
                          4   Red                4 Red
                          5   Magenta            5 Magenta
                          6   Brown              6 Brown
                          7   Gray               7 Gray
                                                 8 Intense black
                     ORing the color value       9 Intense blue
                     with 0x80 produces a        A Intense green
                     blinking color unless       B Intense cyan
                     blinking has been           C Intense red
                     disabled.                   D Intense magenta
                                                 E Intense brown (yellow)
                                                 F Intense gray (white)

                    -------------------------------------------------------

                    On monochrome monitors, the attribute values can differ
                    widely, so you may need to experiment.


------------------  The name that follows the /F option should be the full
 /Fname (full path  drive/directory path name of the help file to use; for
 and name for help  example,
             file)
------------------   THELP /FC:\TC\OWLHELP.TCH
                     THELP /FC:\TC\TCHELP.TCH

                    You can specify multiple help files on the command-line
                    or in the THELP.CFG file.  THELP supports up to eight
                    help files.







                                  - 41 -






------------------  Any of these options displays a summary of THELP's com-
     /H, /?, and ?  mand-line options.
     (display help
           screen)
------------------  This option allows you to reassign a function to a new
  /Kxxyy (reassign  hot key. The option must be followed by the shift state
          hot key)  (xx) and the scan code (yy) of the new key. Virtually
------------------  any shift state/scan code combination may be selected.
                    Here's a quick summary of some common shift states and
                    scan codes:


                    -------------------------------------------------------
                      Shift states (can be OR'ed together):

                       Right Shift   01h
                       Left Shift    02h
                       Ctrl          04h
                       Alt           08h

                      Scan codes:

                       A   1eh   N   31h   0   0bh   F1  3bh
                       B   30h   O   18h   1   02h   F2  3ch
                       C   2eh   P   19h   2   03h   F3  3dh
                       D   20h   Q   10h   3   04h   F4  3eh
                       E   12h   R   13h   4   05h   F5  3fh
                       F   21h   S   1fh   5   06h   F6  40h
                       G   22h   T   14h   6   07h   F7  41h
                       H   23h   U   16h   7   08h   F8  42h
                       I   17h   V   2fh   8   09h   F9  43h
                       J   24h   W   11h   9   0ah   F10 44h
                       K   25h   X   2dh
                       L   26h   Y   15h
                       M   32h   Z   2ch

                      Enhanced keyboards only (may not work with all
                      computers or keyboards):

                       F11 57h
                       F12 58h

                    -------------------------------------------------------








                                  - 42 -






------------------  This option removes THELP from memory. If other TSRs
  /U (remove THELP  have been loaded after THELP, make sure to remove them
      from memory)  before removing THELP.
------------------

------------------  Where:
/Wx,y,w,h (set the    x = window column location (zero based)
   window size and    y = window row location
         location)    w = window width
------------------    h = window height

                    For example, to create a full-screen help window use:

                     /W0,0,80,25



===========================================================================
TLIB
===========================================================================

                    TLIB is a utility that manages libraries of individual
                    .OBJ (object module) files. A library is a convenient
                    tool for dealing with a collection of object modules as
                    a single unit.

  When it modifies  The libraries included with Turbo C++ were built with
       an existing  TLIB. You can use TLIB to build your own libraries, or
     library, TLIB  to modify the Turbo C++ libraries, your own libraries,
  always creates a  libraries furnished by other programmers, or commercial
       copy of the  libraries you've purchased. You can use TLIB to
  original library
with a .BAK exten-  o create a new library from a group of object modules
 sion. Better safe
       than sorry!  o add object modules or other libraries to an existing
                      library

                    o remove object modules from an existing library

                    o replace object modules from an existing library

                    o extract object modules from an existing library

                    o list the contents of a new or existing library







                                  - 43 -






See the section on  TLIB can also create (and include in the library file)
     the /E option  an Extended Dictionary, which may be used to speed up
     (page 49) for  linking.
          details.
                    Although TLIB is not essential for creating executable
                    programs with Turbo C++, it is a useful programming
                    productivity tool. You will find TLIB indispensable for
                    large development projects. If you work with object
                    module libraries developed by others, you can use TLIB
                    to maintain those libraries when necessary.


    Why use object  =======================================================
 module libraries?
                    When you program in C and C++, you often create a
                    collection of useful functions and classes. Because of
                    C and C++'s modularity, you are likely to split those
                    functions into many separately compiled source files.
                    You use only a subset of functions from the entire
                    collection in any particular program. It can become
                    quite tedious, however, to figure out exactly which
                    files you are using. On the other hand, if you always
                    include all the source files, your program becomes
                    extremely large and unwieldy.

                    An object module library solves the problem of managing
                    a collection of functions and classes. When you link
                    your program with a library, the linker scans the
                    library and automatically selects only those modules
                    needed for the current program.


  The TLIB command  =======================================================
              line
  To get a summary  The TLIB command line takes the following general form,
  of TLIB's usage,  where items listed in square brackets ([like this]) are
just type TLIB and  optional:
      press Enter.
                       tlib [/C] [/E] [/Psize] libname [operations] [,
                    listfile]











                                  - 44 -






Table 1.2: TLIB options


---------------------------------------------------------------------------
Option         Description
---------------------------------------------------------------------------

libname        The DOS path name of the library you want to create or
               manage. Every TLIB command must be given a libname.
               Wildcards are not allowed. TLIB assumes an extension of .LIB
               if none is given. We recommend that you do not use an exten-
               sion other than .LIB, since both TCC and TC's project-make
               facility require the .LIB extension in order to recognize
               library files. Note: If the named library does not exist and
               there are add operations, TLIB creates the library.

/C             The case-sensitive flag. This option is not normally used;
               see page 50 for a detailed explanation.

/E             Creates Extended Dictionary; see page 49 for a detailed
               explanation.

/Psize         Sets the library page size to size; see page 49 for a
               detailed explanation.

operations     The list of operations TLIB performs. Operations may appear
               in any order. If you only want to examine the contents of
               the library, don't give any operations.

listfile       The name of the file listing library contents. The listfile
               name (if given) must be preceded by a comma. No listing is
               produced if you don't give a file name. The listing is an
               alphabetical list of each module. The entry for each module
               contains an alphabetical list of each public symbol defined
               in that module. The default extension for the listfile is
               .LST. You can direct the listing to the screen by using the
               listfile name CON, or to the printer by using the name PRN.

---------------------------------------------------------------------------

                    This section summarizes each of these command-line com-
                    ponents; the following sections provide details about
                    using TLIB. For TLIB examples, refer to the "Examples"
                    section on page 51.







                                  - 45 -






------------------  The operation list describes what actions you want TLIB
The operation list  to do. It consists of a sequence of operations given
------------------  one after the other. Each operation consists of a one-
                    or two-character action symbol followed by a file or
                    module name. You can put whitespace around either the
                    action symbol or the file or module name, but not in
                    the middle of a two-character action or in a name.

                    You can put as many operations as you like on the com-
                    mand line, up to DOS's COMMAND.COM-imposed line-length
                    limit of 127 characters. The order of the operations is
                    not important. TLIB always applies the operations in a
                    specific order:

                    1. All extract operations are done first.

                    2. All remove operations are done next.

                    3. All add operations are done last.

                    You can replace a module by first removing it, then
                    adding the replacement module.


                    File and module names
                    =======================================================

                    TLIB finds the name of a module by taking the given
                    file name and stripping any drive, path, and extension
                    information from it. (Typically, drive, path, and
                    extension are not given.)

                    Note that TLIB always assumes reasonable defaults. For
                    example, to add a module that has an .OBJ extension
                    from the current directory, you only need to supply the
                    module name, not the path and .OBJ extension.

                    Wildcards are never allowed in file or module names.


                    TLIB operations
                    =======================================================

                    TLIB recognizes three action symbols (*, +, *), which
                    you can use singly or combined in pairs for a total of
                    five distinct operations. The order of the characters





                                  - 46 -






                    is not important for operations that use a pair of
                    characters. The action symbols and what they do are
                    listed here:


                    -------------------------------------------------------
     TLIB action      Action
         symbols      symbol  Name           Description
                    -------------------------------------------------------

       To create a      +     Add            TLIB adds the named file to
      library, add                           the library. If the file has
      modules to a                           no extension given, TLIB
 library that does                           assumes an extension of .OBJ.
    not yet exist.                           If the file is itself a
                                             library (with a .LIB exten-
                                             sion), then the operation adds
                                             all of the modules in the
                                             named library to the target
                                             library.

                                             If a module being added
                                             already exists, TLIB displays
                                             a message and does not add the
                                             new module.

                        -     Remove         TLIB removes the named module
                                             from the library. If the
                                             module does not exist in the
                                             library, TLIB displays a
                                             message.

                                             A remove operation only needs
                                             a module name. TLIB allows you
                                             to enter a full path name with
                                             drive and extension included,
                                             but ignores everything except
                                             the module name.

                        *     Extract        TLIB creates the named file by
                                             copying the corresponding
                                             module from the library to the
                                             file. If the module does not
                                             exist, TLIB displays a message
                                             and does not create a file. If
                                             the named file already exists,
                                             it is overwritten.




                                  - 47 -






                    Table 1.3: TLIB action symbols (continued)_____________

You can't directly      -*    Extract &      TLIB copies the named module
 rename modules in      *-    Remove         to the corresponding file name
     a library. To                           and then removes it from the
  rename a module,                           library. This is just
extract and remove                           shorthand for an extract
    it, rename the                           followed by a remove
file just created,                           operation.
  then add it back
 into the library.      -+    Replace        TLIB replaces the named module
                        +-                   with the corresponding file.
                                             This is just shorthand for a
                                             remove followed by an add
                                             operation.

                    -------------------------------------------------------


    Using response  =======================================================
             files
                    When you are dealing with a large number of operations,
                    or if you find yourself repeating certain sets of
                    operations over and over, you will probably want to
                    start using response files. A response file is simply
                    an ASCII text file (which can be created with the Turbo
                    C++ editor) that contains all or part of a TLIB com-
                    mand. Using response files, you can build TLIB commands
                    larger than would fit on one DOS command line.

See "Examples" for  To use a response file pathname, specify @pathname at
 a sample response  any position on the TLIB command line.
   file and a TLIB
      command line  o More than one line of text can make up a response
 incorporating it.    file; you use the "and" character (&) at the end of a
                      line to indicate that another line follows.

                    o You don't need to put the entire TLIB command in the
                      response file; the file can provide a portion of the
                      TLIB command line, and you can type in the rest.

                    o You can use more than one response file in a single
                      TLIB command line.








                                  - 48 -






       Creating an  =======================================================
          extended
dictionary: The /E  To speed up linking with large library files (such as
            option  the standard Cx.LIB library), you can direct TLIB to
                    create an extended dictionary and append it to the
                    library file. This dictionary contains, in a very
                    compact form, information that is not included in the
                    standard library dictionary. This information enables
                    TLINK to process library files faster.

                    To create an extended dictionary for a library that is
                    being modified, use the /E option when you invoke TLIB
                    to add, remove, or replace modules in the library. To
                    create an extended dictionary for an existing library
                    that you don't want to modify, use the /E option and
                    ask TLIB to remove a nonexistent module from the
                    library. TLIB will display a warning that the specified
                    module was not found in the library, but it will also
                    create an extended dictionary for the specified
                    library. For example, if you enter

                       tlib /E mylib -bogus

                    TLINK will ignore the debugging information in a
                    library that has an extended dictionary, unless the /e
                    option is used on the TLINK command line.


  Setting the page  =======================================================
      size: The /P
            option  Every DOS library file contains a dictionary (which
                    appears at the end of the .LIB file, following all of
                    the object modules). For each module in the library,
                    this dictionary contains a 16-bit address of that
                    particular module within the .LIB file; this address is
                    given in terms of the library page size (it defaults to
                    16 bytes).

                    The library page size determines the maximum combined
                    size of all object modules in the library--it cannot
                    exceed 65,536 pages. The default (and minimum) page
                    size of 16 bytes allows a library of about 1 MB in
                    size. To create a larger library, the page size must be
                    increased using the /P option; the page size must be a







                                  - 49 -






                    power of 2, and it may not be smaller than 16 or larger
                    than 32,768.

                    All modules in the library must start on a page
                    boundary. For example, in a library with a page size of
                    32 (the lowest possible page size higher than the
                    default 16), on the average 16 bytes will be lost per
                    object module in padding. If you attempt to create a
                    library that is too large for the given page size, TLIB
                    will issue an error message and suggest that you use /P
                    with the next available higher page size.


          Advanced  =======================================================
 operation: The /C
            option  When you add a module to a library, TLIB maintains a
                    dictionary of all public symbols defined in the modules
                    of the library. All symbols in the library must be
                    distinct. If you try to add a module to the library
                    that would cause a duplicate symbol, TLIB displays a
                    message and won't add the module.

                    Normally, when TLIB checks for duplicate symbols in the
                    library, uppercase and lowercase letters are not
                    considered as distinct. For example, the symbols lookup
                    and LOOKUP are treated as duplicates. Since C and C++
                    do treat uppercase and lowercase letters as distinct,
                    use the /C option to add a module to a library that
                    includes a symbol differing only in case from one
                    already in the library. The /C option tells TLIB to
                    accept a module with symbols in it that differ only in
                    case from symbols already in the library.

If you want to use  It may seem odd that, without the /C option, TLIB
  the library with  rejects symbols that differ only in case, especially
 other linkers (or  since C and C++ are case-sensitive languages. The
allow other people  reason is that some linkers fail to distinguish between
to use the library  symbols in a library that differ only in case. Such
        with other  linkers, for example, will treat stars, Stars, and
linkers), for your  STARS as the same identifier. TLINK, on the other hand,
own protection you  has no problem distinguishing uppercase and lowercase
should not use the  symbols, and it will properly accept a library contain-
        /C option.  ing symbols that differ only in case. In this example,
                    then, Turbo C++ would treat stars, Stars, and STARS as
                    three separate identifiers. As long as you use the
                    library only with TLINK, you can use the TLIB /C option
                    without any problems.




                                  - 50 -






          Examples  =======================================================

                    Here are some simple examples demonstrating the
                    different things you can do with TLIB.

                    1. To create a library named MYLIB.LIB with modules
                       X.OBJ, Y.OBJ, and Z.OBJ, type
                        tlib mylib +x +y +z

                    2. To create a library as in #1 and get a listing in
                       MYLIB.LST too, type
                        tlib mylib +x +y +z, mylib.lst

                    3. To get a listing in CS.LST of an existing library
                       CS.LIB, type
                        tlib cs, cs.lst

                    4. To replace module X.OBJ with a new copy, add A.OBJ
                       and delete Z.OBJ from MYLIB.LIB, type
                        tlib mylib -+x +a -z

                    5. To extract module Y.OBJ from MYLIB.LIB and get a
                       listing in MYLIB.LST, type
                        tlib mylib *y, mylib.lst

                    6. To create a new library named ALPHA, with modules
                       A.OBJ, B.OBJ, ..., G.OBJ using a response file:
                        
                       First create a text file, ALPHA.RSP, with
                        +a.obj +b.obj +c.obj &
                               +d.obj +e.obj +f.obj &
                               +g.obj
                        
                       Then use the TLIB command, which produces a listing
                       file named ALPHA.LST:
                        tlib alpha @alpha.rsp, alpha.lst



===========================================================================
TOUCH
===========================================================================

                    There are times when you want to force a particular
                    target file to be recompiled or rebuilt, even though no
                    changes have been made to its sources. One way to do





                                  - 51 -






                    this is to use the TOUCH utility. TOUCH changes the
                    date and time of one or more files to the current date
                    and time, making it "newer" than the files that depend
                    on it.

                    You can force MAKE to rebuild a target file by touching
                    one of the files that target depends on. To touch a
                    file (or files), type

   You can use the    touch filename [filename ...]
   DOS wildcards *
 and ? with TOUCH.  at the DOS prompt. TOUCH will then update the file's
                    creation date(s). Once you do this, you can invoke MAKE
                    to rebuild the touched target file(s).

        Important!  Before you use the TOUCH utility, it's vitally
                    important to set your system's internal clock to the
                    proper date and time. If you're using an IBM PC, XT, or
                    compatible that doesn't have a battery-powered clock,
                    don't forget to set the time and date using the DOS
                    TIME and DATE commands. Failing to do this will keep
                    both TOUCH and MAKE from working properly.



===========================================================================
TRANCOPY: A project transfer item utility
===========================================================================

                    TRANCOPY copies transfer items from one project to
                    another. The syntax is

                     TRANCOPY [-r] Source[.PRJ] Dest[.PRJ]

                    TRANCOPY merges the transfer items in Source with the
                    transfer in Dest; Dest gets the new transfer items.

                    If the -r option is used, the set of the transfer items
                    in Dest is replaced by the set of transfer items in
                    Source.











                                  - 52 -






===========================================================================
TRIGRAPH: A character-conversion utility
===========================================================================

                    Trigraphs are three-character sequences that replace
                    certain characters used in the C language that are not
                    available on some keyboards. Translating trigraphs in
                    the compiler would slow compilation down considerably,
                    so Turbo C++ provides a filter named TRIGRAPH.EXE to
                    handle trigraph sequences when you need to. The syntax
                    for invoking this program is as follows:

                       TRIGRAPH [-u] file(s) [file(s) ...]

                    The following table shows the trigraph sequences that
                    TRIGRAPH.EXE recognizes:


                    -------------------------------------------------------
                      Trigraph  Character
                    -------------------------------------------------------


                       ??=        #
                       ??(        [
                       ??/        \
                       ??)        ]
                       ??'        ^
                       ??<        {
                       ??!        |
                       ??>        }
                       ??-        ~

                    -------------------------------------------------------

                    TRIGRAPH.EXE works in two directions: It can convert
                    all trigraphs to their single-character representation,
                    and it can convert single characters to their trigraph
                    representation. Ordinarily, TRIGRAPH.EXE converts
                    trigraphs to single characters. You can specify the
                    inverse conversion with the -u (UNDO) command-line
                    option, which must come before any file names on the
                    command line.

                    TRIGRAPH.EXE takes any number of file specifiers,
                    including wildcards, on the command line. For each file
                    specified, it creates a backup copy of the file with
                    the original file name and an extension of .BAK, and



                                  - 53 -






                    creates a new file with the original file name and the
                    appropriate conversions performed. For example,

                       trigraph test.c test1.c

                    removes all trigraphs from the two files TEST.C and
                    TEST1.C, creating backup files TEST.BAK and TEST1.BAK.

                    As another example, the following command inserts
                    trigraphs into all the files with the extension .C, and
                    makes backup copies of all those files, giving them the
                    extension .BAK.

                       trigraph -u *.c



===========================================================================
Transfer macros
===========================================================================

                    The IDE recognizes certain strings of characters called
                    transfer macros in the parameter string of the
                    Modify/New Transfer Item dialog box. There are three
                    kinds of macros: state, file name, and instruction.


      The transfer  State macros
 macros are listed  =======================================================
alphabetically and
 described in more  State macros are expanded according to the state of the
detail starting on  IDE. The state macros are
          page 55.
                    $COL                          $ERRNAME
                    $CONFIG                       $INC
                    $DEF                          $LIB
                    $ERRCOL                       $LINE
                    $ERRLINE                      $PRJNAME


                    File name macros
                    =======================================================

                    File name macros are actually functions that take file
                    names as arguments and return various parts of the file
                    name. They allow you to build up new file name
                    specifications from existing file names. For example,
                    you can pass TDUMP a macro like this:



                                  - 54 -






                       $DIR($EXENAME)$NAME($EDNAME).OBJ

                    This macro gives you the output directory path, the
                    file name only in the active Edit window, and an
                    explicit extension. If your current directory is
                    C:\WORK, your output directory is TEST, and the active
                    editor contains MYPROG.C, then TDUMP receives the
                    parameter

                       C:\WORK\TEST\MYPROG.OBJ

                    The file name macros are

                    $DIR                          $EXT()
                    $DRIVE()                      $NAME()
                    $EDNAME                       $OUTNAME
                    $EXENAME


                    Instruction macros
                    =======================================================

                    Instruction macros tell the IDE to perform some action
                    or make some setting. The instruction macros are

                    $CAP EDIT                     $PROMPT
                    $CAP MSG(filter)              $SAVE ALL
                    $DEP()                        $SAVE CUR
                    $MEM(kb to reserve)           $SAVE PROMPT
                    $NOSWAP                       $TASM
                    

                    $CAP EDIT: This macro tells the IDE to redirect program
                    output into a standard file. After the transfer program
                    is completed, a new editor window is created, and the
                    captured output is displayed. The captured output
                    resides in a special Edit window titled Transfer
                    Output.

                    For $CAP EDIT to work correctly, the transfer program
                    must write to DOS standard output.










                                  - 55 -






   You can use any  $CAP MSG(filter): Captures program output into the
  program that has  Message window, using filter as a DOS filter for
     line-oriented  converting program output into Message window format.
   messages output
    (file and line  We've provided several filters for this macro:
 number) with this  GREP2MSG.EXE for GREP and TASM2MSG.EXE for Turbo
            macro.  Assembler (TASM). We've included the source code for
                    these filters so you can write your own filters for
                    other transfer programs you install.

                    $COL: Column number of current editor. If the active
                    window is not an editor, then the string is set to 0.

                    $CONFIG: Complete file name of the current
                    configuration file. This is a null string if no
                    configuration file is defined. This macro is intended
                    for use by programs that access or modify the
                    configuration file. Besides providing the name of the
                    file, this macro causes the current configuration to be
                    saved (if modified) and reloaded when control returns
                    to the IDE.

 TEML is a Pascal-  Use this macro with the Turbo Editor Macro Language
like language that  (TEML) compiler. With it, you can edit the TEML script
 has many built-in  file in an editor and then invoke the Turbo Editor
  primitive editor  Macro Compiler (TEMC) to process the script. When the
 commands. Its use  configuration file is reloaded, your new or modified
  is documented in  editor commands will be in effect. When installing TEMC
        this file.  as a transfer item, use the following command line:

                       $EDNAME $CONFIG

                    This assumes the current Edit window contains the TEML
                    script file to be processed.

                    $DEF: Pulls in the contents of the Options|Compiler|
                    Code Generation "Defines" type-in box. Use this macro
                    to specify define directives to an external translator.

This macro is only  $DEP(): This macro provides the ability to
       used by the  automatically rebuild resources as part of a project
  project manager.  make if one of the resource components has been
                    modified.

                    $DIR(): Directory of the file argument, full path.

                    $DRIVE(): Drive of the file argument, in the form D:.




                                  - 56 -






                    $EDNAME: Complete file name of file in active editor.
                    This is a null string if the active window is not an
                    editor.

                    $ERRCOL: Column number of current error in file
                    $ERRNAME. If there are no messages, then string is
                    expanded to null string.

                    $ERRLINE: Line number of current error in file
                    $ERRNAME. If there are no messages, then string is
                    expanded to null string.

                    $ERRNAME: Complete file name of file referred to by the
                    selected messages in the Message window. This is a null
                    string if there are no messages or the currently
                    selected message does not refer to a file.

                    $EXENAME: Program's file name (including output path),
                    based on the project name or, if there is no project
                    defined, then the name of the .EXE that would be
                    produced from the active editor window.

                    $EXT(): Extension of the file argument; this includes
                    the dot (for example, .CPP).

                    $INC: Pulls in the contents of the Options|Directories|
                    Include Directories type-in box.

                    $LIB: Pulls in the contents of the Options|Directories|
                    Library Directories type-in box.

                    $LINE: Line number of current editor. If the active
                    window is not an editor, then the string is set to 0.

                    $MEM(Kb to reserve): This macro tells the IDE how much
                    memory to try to give the transfer program. The IDE
                    gives up as much memory as possible, to either the
                    amount specified or the maximum available, whichever is
                    smaller. You'll get an error if no memory is specified.

                    $NAME(): Name part of the file argument; does not
                    include the dot.

                    $NOSWAP: This macro tells the IDE not to swap to the
                    User Screen when running the program. It pops up a box
                    that indicates which transfer program is running. Use
                    this macro in conjunction with $CAP.




                                  - 57 -






                    $OUTNAME: This macro expands to the path and file name
                    that appear in the Project|Local Options Output Path
                    type-in box (in the active edit window). For example,
                    if the project contains STARS.C, the default Output
                    Path type-in is STARS.OBJ. So if STARS.C is in the
                    active edit window, $OUTNAME expands to STARS.OBJ. If
                    you've edited the type-in box so it says ..\MOON.XYZ,
                    $OUTNAME will expand to ..\MOON.XYZ. This macro is
                    useful when you are specifying modules for your user-
                    defined translators. For example, you could define a
                    TLIB translator and set the command line to

                    TLIB MYLIB +$OUTNAME

                    which adds the object module of the file in the active
                    edit window to the library MYLIB.

                    $PRJNAME: The current project file. Null string if no
                    project is defined.

                    $PROMPT: This macro tells the IDE to display the
                    expanded parameter string before calling the transfer
                    program. The command line that will be passed is
                    displayed in a dialog box. This allows you to change or
                    add to the string before it is passed.The position of
                    $PROMPT command in the command line determines what is
                    shown in the dialog prompt box. You can place constant
                    parameters in the command line by placing them before
                    $PROMPT. For example, the /c in

                       /c $PROMPT dir

                    is constant and doesn't show in the dialog box, but dir
                    can be edited before the command is run.

                    $SAVE ALL: This macro tells the IDE to save all
                    modified files in all Edit windows that have been
                    modified, without prompting.

                    $SAVE CUR: This macro tells the IDE to save the file in
                    the current editor if it has been modified. This
                    ensures that the invoked program will use the latest
                    version of the source file.

                    $SAVE PROMPT: This macro tells the IDE to prompt when
                    there are unsaved files in editor windows. You will be
                    asked if you want to save any unsaved files.




                                  - 58 -






                    $TASM: This macro is predefined for use with Turbo
                    Assembler. It uses the TASM2MSG filter to trap TASM
                    messages. $TASM is essentially shorthand for this:

                     $NOSWAP $SAVE CUR $CAP MSG(TASM2MSG) $EDNAME,$OUTNAME

                    $WRITEMSG(filename): This macro copies the contents of
                    the Message window to the specified ASCII file. The
                    translator can parse the file and act on the messages
                    so desired. For example, $WRITEMSG(C:\MESSAGES.TXT)
                    writes to the file MESSAGES.TXT on your root directory.


       Running DOS  =======================================================
          commands
                    If you want to run DOS commands from within the
                    integrated environment, you can set up a simple
                    transfer macro that will let you do so. Just add this
                    transfer item:

                       command /c $MEM(128) $PROMPT

                    When you invoke this transfer item, a dialog box
                    appears and prompts you for DOS input. Since the
                    $PROMPT command appears later in the string, the text
                    command /c won't show up in the dialog's input box.
                    This lets you just type dir, chkdsk, del *.*, or
                    whatever DOS command you want to run.


   Transfer memory  =======================================================
          settings
                    Different programs have different memory needs. For
                    example, GREP can run in very little memory, where many
                    popular editors require 200-300K to work well.

                    If you use the $MEM() macro, you can specify (on a
                    program-by-program basis) how much memory the IDE
                    should give to the transfer programs. The less memory
                    you devote to a transfer program, the quicker the
                    transfer to and from the program occurs.

                    There may be some cases where the IDE cannot give up as
                    much memory as you requested. When this happens, the
                    IDE gives up as much as it can. There are certain
                    states in the IDE that require more memory than others;
                    for example, while debugging a program, the IDE will




                                  - 59 -






                    tie up more resources than when not debugging. Use
                    Program Reset (Ctrl-F2) to free up debugging memory.

                    In those cases where you want the IDE to give up all
                    its memory, give it a large number, like 640K. How much
                    memory is actually given up is dependent on how much
                    you have when you start Turbo C++.



===========================================================================
Turbo Editor macros
===========================================================================

                    TEMC.EXE is an editor macro compiler for the IDE. It
                    processes a script file that defines editor macros and
                    key bindings, and produces a configuration file that is
                    read by the IDE to define the effects of keyboard
                    commands in the editor.

                    The file DEFAULTS.TEM contains the default macro
                    definitions and key bindings built into the IDE editor.
                    It serves as an example script, as well as a base from
                    which to customize the editor.



===========================================================================
TEMC command line
===========================================================================

                    TEMC is invoked from the DOS command line. Type

                     temc [-c] [-u] <script file> <config file>

                    The script file extension is .TEM if not specified
                    otherwise. The configuration file extensions is assumed
                    to be .TC.

                    The configuration file need not exist. If it does not
                    exist, it is created. The optional -c switch can also
                    be specified as /c, and can appear in any argument
                    position on the command line. If you use this option,
                    any existing command table in your configuration file
                    is thrown away before TEMC processes the script file.
                    When -c is not used, the key bindings in the script
                    file are merged with those already defined in the
                    configuration file.



                                  - 60 -






                    TEMC by default modifies the commands used by the IDE
                    when the Alternate command set is specified in Options|
                    Environment|Preferences. The optional -u switch, which
                    can also be specified as /u, causes TEMC to modify the
                    CUA command set instead.

                    You can use DEFAULTS.TEM to re-create exactly the
                    default settings of the Alternate command set.  This
                    file is included as both a sample script file and as
                    the default command table. You can copy it and modify
                    it for your own use. A file named CMACROS.TEM is
                    provided with Turbo C++; this file contains many useful
                    enhancements to the IDE for C and C++ programming that
                    you may wish to install.



===========================================================================
Syntax
===========================================================================

                    The syntax to define a macro is

                     MACRO <macroname>
                       <command1>;
                       [ <command2>; ... ]
                     END;

                    <macroname> can consist of anything that is a legal C
                    symbol, and <command> can be either the name of another
                    predefined macro or a predefined TEMC editor command. A
                    list of editor commands and what they do follows.

                    When you define your macro, the following points are
                    valid:

                    1. A statement defines either a named macro or a key
                       binding.

                    2. Spaces and new lines are optional.

                    3. Comments are in C-style /* ... */ pairs.

                    4. Unlike C, TEMC's language is case insensitive.

                    5. Some of the predefined editor commands have a syntax
                       that looks like a C function call with one argument.
                       For example,



                                  - 61 -






                        
                        SetMark(5);
                        
                       Depending on the command, the argumment is either a
                       decimal integer constant, a character constant, or a
                       string literal. All are specified using C syntax.

                    Here's an example of a macro definition from
                    DEFAULTS.TEM:

                     MACRO MacScrollUp
                       ScrollScreenUp; FixCursorPos;
                     END;

                    The syntax to define a key binding is

                     <key-sequence>: <command>;

                    or

                     <key-sequence>: BEGIN <command1>; [ <command2>; ... ]
                     END;

                    The <key-sequence> is either a key (a character
                    optionally preceded by Ctrl or Alt), or a series of
                    keys separated by a plus sign (+). Note that the
                    specification of the key characters themselves is case
                    sensitive. For example, Ctrl-k+B is different than
                    Ctrl-k+b, even though the latter is the same as CTRL-
                    K+b.

                    Whitespace is allowed between the key-sequence and the
                    colon, and each <command> can be either the name of a
                    previously defined macro, or one of the predefined
                    editor commands listed in Table 1.1.



===========================================================================
Key codes
===========================================================================

                    The IDE editor makes use of an extended character set
                    that includes key combinations not normally available
                    to DOS programs. Key codes can be specified in a script
                    through any combination of the symbols "Ctrl-",
                    "Shift-" "Alt-" and a character.




                                  - 62 -






                    Some keys cannot be entered directly into a TEMC
                    script. Those keys can be referred to by their names,
                    as described in the following table.

                    Any key in a sequence--except the first key--can be
                    preceded by one of the characters ^ or @. The caret (^)
                    indicates that any combination of case and "Ctrl" can
                    be used to type the key; that is, lowercase, uppercase,
                    or control characters. The @ sign is used to indicate
                    that case is insignificant for the following character,
                    although "Ctrl" is not accepted. For example,

                    o Ctrl-k+b specifies a Ctrl-K followed by a lowercase
                      b.

                    o Ctrl-k+^b specifies a Ctrl-K followed by any of b, B,
                      or Ctrl-B.

                    o Ctrl-k+@B specifies Ctrl-K followed by either b or B.


        Named keys  =======================================================

                    Key are specified as letters, numbers, or characters,
                    optionally preceded by one or more of Ctrl-, Alt- or
                    Shift-. The following names specify keys that cannot be
                    typed as themselves in the TEMC syntax.


                    -------------------------------------------------------
                       Key name    Notes
                    -------------------------------------------------------

                       Home
                       End
                       PgUp
                       PgDn
                       LfAr        Left arrow
                       RgAr        Right arrow
                       UpAr        Up arrow
                       DnAr        Down arrow
                       Ins
                       Del
                       Enter
                       Return      Same as Enter
                       BkSp        Backspace
                       Tab
                       BkTab       No longer available, use Shift-Tab



                                  - 63 -






                       Esc
                       Star        * key on the numeric keypad
                       Minus       - key on the numeric keypad
                       Plus        + key on the numeric keypad
                       Space       Spacebar
                       PrtSc
                       F1 to F10   Function keys

                    -------------------------------------------------------



===========================================================================
Predefined editor commands
===========================================================================

                    TEMC lets you use built-in editor commands and user-
                    defined macros as commands within macros
                    interchangeably, as long as you don't create any loops
                    by having two macros calling each other, even via
                    intermediate macros. Note that some commands cause an
                    escape from the editor to the surrounding IDE, for
                    example, by bringing up a dialog box. Your macro will
                    "pause" until control returns to the editor.

                    A list of all predefined TEMC editor commands is shown
                    next. Commands that cause an escape from the editor
                    follow.



                    -------------------------------------------------------
     TEMC editor    Command name                What the editor does
        commands    -------------------------------------------------------

                    BackspaceDelete             Deletes character before
                                                the cursor.

                    BottomOfScreen              Moves cursor to the bottom
                                                line of the current window,
                                                leaving column unchanged.

                    CenterFixScreenPos          Adjusts the screen display
                                                to ensure the cursor is
                                                visible. If any adjustment
                                                is necessary, adjust the
                                                display so the cursor is




                                  - 64 -






                    Table 1.4: TEMC editor commands (continued)____________

                                                close to being centered in
                                                the window.

                    CopyBlock                   If there is a valid and
                                                highlighted (selected)
                                                text block, then at the
                                                cursor location, inserts a
                                                copy of the characters that
                                                are selected and makes that
                                                the new selected text
                                                location.

                    CursorCharLeft              Moves cursor left over one
                                                character. This command
                                                will skip over tab
                                                characters and move to the
                                                end of the previous line.

                    CursorCharRight             Moves cursor right over one
                                                character. This  command
                                                will skip over tab
                                                characters and advance to
                                                the beginning of the next
                                                line.

                    CursorDown                  Moves cursor down one row.

                    CursorLeft                  Moves cursor left one
                                                screen column.

                    CursorRight                 Moves cursor right one
                                                screen column.

                    CursorSwitchedLeft          Like CursorLeft, but pays
                                                attention to cursor through
                                                tab option setting (see
                                                SetCursorThroughTabMode).

                    CursorSwitchedRight         Like CursorRight, but pays
                                                attention to cursor
                                                through tab option setting
                                                (see
                                                SetCursorThroughTabMode).

                    CursorUp                    Moves cursor up one row.




                                  - 65 -






                    Table 1.4: TEMC editor commands (continued)____________

                    DeleteBlock                 If there is a valid and
                                                highlighted (selected) text
                                                block, deletes the
                                                characters that are in it.

                    DeleteChar                  Deletes the character at
                                                the current cursor
                                                location.

                    DeleteLine                  Deletes the current line.

                    DeleteToEOL                 Deletes all characters in
                                                the current line, leaving a
                                                zero-length line.

                    DeleteWord                  Deletes from cursor to
                                                beginning of next word.

                    EndCursor                   Moves cursor to end of file
                                                buffer.

                    ExtendBlockBeg              Initiates a series of
                                                commands that will select a
                                                block of text between the
                                                initial and ending
                                                positions of the cursor.

                    ExtendBlockEnd              Ends a series of commands
                                                begun by ExtendBlockBeg.

                    FixCursorPos                Ensures that the cursor
                                                value specifies a row
                                                between 1 and the number of
                                                lines in the buffer, a
                                                column greater than 0. If
                                                the cursor through tab
                                                option is not set, the
                                                cursor is not placed in the
                                                middle of a tab character
                                                (see
                                                SetCursorThroughTabMode).

                    FixScreenPos                Adjusts the screen display
                                                to ensure the cursor is
                                                visible.




                                  - 66 -






                    Table 1.4: TEMC editor commands (continued)____________

                    FullPaintScreen             Redraws the entire window,
                                                making no assumptions about
                                                what is onscreen.

                    HideBlock                   Sets a flag indicating that
                                                the selected text should
                                                not be highlighted.

                    HighlightBlock              Sets a flag indicating that
                                                if the beginning and end
                                                selected text markers are
                                                valid, the selected text
                                                should be highlighted.

                    HomeCursor                  Moves cursor to beginning
                                                of the file buffer.

                    IndentBlock                 Inserts a space at the
                                                beginning of each line in
                                                the highlighted (selected)
                                                text.

                    InsertText                  Inserts the literal
                                                "string" in the buffer at
                                                the current cursor
                                                location. Use the syntax
                                                InsertText(string) to call
                                                this command.

                    LeftOfLine                  Moves cursor to beginning
                                                of the current line.

                    LiteralChar                 Inserts the character at
                                                the current cursor
                                                location, without doing any
                                                special processing for
                                                newline, tab characters,
                                                etc. Use the syntax
                                                LiteralChar(c), where c is
                                                a character or integer
                                                value.

                    MarkBufModified             Sets a flag indicating that
                                                the contents of the buffer
                                                are different than what is




                                  - 67 -






                    Table 1.4: TEMC editor commands (continued)____________

                                                in the corresponding disk
                                                file.

                    MarkBufUnModified           Clears a flag, thus
                                                indicating that the
                                                contents of the buffer can
                                                be assumed to be identical
                                                to what is in the disk
                                                file.

                    MatchPairBackward           Same as MatchPairForward
                                                except if the cursor is on
                                                a ' or ", searches backward
                                                for the matching character.

                    MatchPairForward            If the cursor is on one of
                                                the characters (, ), {, },
                                                [, ], or on the first
                                                character of one of the
                                                pairs /* or */, searches in
                                                the appropriate direction
                                                for the closest instance of
                                                the matching delimiter. If
                                                the cursor is on the
                                                character ' or ", searches
                                                forward for the matching
                                                character. If a match is
                                                found, places the cursor
                                                there.

                    MoveBlock                   Like CopyBlock, but also
                                                deletes the original
                                                selected text.

                    MoveToBlockBeg              Moves cursor to the
                                                location marked as the
                                                beginning of the selected
                                                text.

                    MoveToBlockEnd              Moves cursor to the
                                                location marked as the end
                                                of the selected text.

                    MoveToMark                  Moves the cursor to the
                                                location saved with
                                                SetMark(n) command. Use the



                                  - 68 -






                    Table 1.4: TEMC editor commands (continued)____________

                                                syntax MoveToMark(n), where
                                                n is a one-digit number,
                                                0-9.

                    MoveToPrevPos               Moves the cursor to the
                                                location specified by the
                                                "previous position marker."

                    MoveToTempPos               Moves the cursor to the
                                                saved temporary marker.

                    NullCmd                     No operation. Calls the
                                                editor, but performs no
                                                function. Can be used to
                                                cause a keystroke to have
                                                no effect.

                    OutdentBlock                Deletes a leading space, if
                                                any, from the beginning of
                                                each line in the
                                                highlighted (selected)
                                                text.

                    PageDown                    Moves cursor down by number
                                                of lines in the window.

                    PageScreenDown              Scrolls screen down by
                                                numer of lines in the
                                                window, leaving cursor
                                                position unchanged.

                    PageScreenUp                Scrolls screen up by numer
                                                of lines in the window,
                                                leaving cursor position
                                                unchanged.

                    PageUp                      Moves cursor up by number
                                                of lines in the window.

                    PaintScreen                 Redraws the entire window,
                                                assuming that the screen
                                                still correctly displays
                                                what the editor last drew
                                                on it.





                                  - 69 -






                    Table 1.4: TEMC editor commands (continued)____________

                    ReDo                        Performs an Redo operation.
                                                Exactly what happens
                                                depends on the option
                                                settings.

                    RightOfLine                 Moves cursor to end of
                                                current line.

                    RightOfWord                 Moves cursor to the next
                                                column that follows the end
                                                of a word.

                    ScrollScreenDown            Scrolls screen down one
                                                line, leaving cursor
                                                position unchanged.

                    ScrollScreenUp              Scrolls screen up one line,
                                                leaving cursor position
                                                unchanged.

                    SetAutoIndent               Sets the Auto Indent option
                                                On.

                    SetAutoOutdent              Sets the Backspace
                                                Unindents option On.

                    SetBlockBeg                 Sets the beginning of the
                                                selected text to be the
                                                character at the current
                                                cursor location.

                    SetBlockEnd                 Sets the end of the
                                                selected text to be the
                                                character at the current
                                                cursor location.

                    SetCursorThroughTabMode     Sets the Cursor Through
                                                Tabs option On.

                    SetInsertMode               Sets Insert/Overwrite
                                                option to Insert.

                    SetMark                     Sets a marker to point to
                                                the character at the
                                                current cursor location, so
                                                a later MoveToMark(n)



                                  - 70 -






                    Table 1.4: TEMC editor commands (continued)____________

                                                comand can restore the
                                                cursor. Use the syntax
                                                SetMark(n), where n is a
                                                one digit number, 0-9.

                    SetOptimalFillMode          Sets Optimal Fill option
                                                On.

                    SetPrevPos                  Sets a marker (the previous
                                                position marker) to point
                                                to the character at the
                                                current cursor location.
                                                This marker location
                                                changes only by a call to
                                                SetPrevPos or SwapPrevPos.

                    SetTabbingMode              Sets Use Tab Char option
                                                On.

                    SetTempPos                  Saves the cursor location
                                                in a temporary marker that
                                                can be used by some
                                                internal editor commands.
                                                This is not a practical
                                                application in user-defined
                                                macros. Use SetMark
                                                instead.

                    SmartRefreshScreen          Redraws the window,
                                                skipping any portions that
                                                the editor is sure are
                                                unmodified since the last
                                                redraw.

                    SmartTab                    Inserts space or tab
                                                characters in accordance
                                                with the current settings
                                                of the Use Tab Char option,
                                                Tab Width.

                    SwapPrevPos                 Exchanges the values of the
                                                cursor and the "previous
                                                position marker."

                    ToggleAutoIndent            Toggles the state of the
                                                Auto Indent option.



                                  - 71 -






                    Table 1.4: TEMC editor commands (continued)____________

                    ToggleAutoOutdent           Toggles the state of the
                                                Backspace Unindents option.

                    ToggleCursorThroughTabMode  Toggles the state of the
                                                Cursor Through Tabs option.

                    ToggleHideBlock             Toggles the state of the
                                                highlight (selected) text
                                                flag (see HighlightBlock).

                    ToggleInsert                Toggles state of
                                                Insert/Overwrite option.

                    ToggleOptimalFillMode       Toggles state of Optimal
                                                Fill option.

                    ToggleTabbingMode           Toggles state of Use Tab
                                                Char option.

                    TopOfScreen                 Moves cursor to the top
                                                line currently displayed in
                                                the window, leaving column
                                                unchanged.

                    UnDo                        Performs an Undo operation.
                                                Exactly what happens
                                                depends on the option
                                                settings.

                    WordLeft                    Moves cursor to beginning
                                                of previous word, or to end
                                                of previous line, whichever
                                                is first.

                    WordRight                   Moves cursor to beginning
                                                of next word, or to the end
                                                of a line, whichever is
                                                first.

                    -------------------------------------------------------


                    The following commands cause an exit from the editor,
                    for example, by bringing up a dialog box. The macro
                    resumes when the editor window regains the focus.




                                  - 72 -






                    The keys listed next to some of the commands below are
                    the ones used by default when the Alternate mode of the
                    IDE is selected.

                    AddWatch            Adds a watch item (Ctrl-F7).
                    ChangeDirectory     Opens a dialog box for changing the
                                        current directory.
                    ChangeModeFlags     Used after a command such as
                                        ToggleInsert which changes the
                                        state of an editor option switch.
                                        Causes the IDE to update various
                                        menu items and/or icons.
                    ClipCopy            Copys selected text to Clipboard
                                        (Ctrl-Ins).
                    ClipCut             Cuts selected text to Clipboard
                                        (Shift-Del).
                    ClipPaste           Pastes Clipboard into buffer at
                                        cursor (Shift-Ins).
                    ClipShow            Shows Clipboard (no hot key
                                        defined).
                    CloseWindow         Closes editor window (Alt-F3).
                    CompileFile         Compiles current buffer (Alt-F9).
                    CompileMenu         Selects Compile menu (Alt-C).
                    CompilerOptions     Selects the Options Compiler menu
                    DebugMenu           Selects Debug menu (Alt-D).
                    EditMenu            elects Edit menu (Alt-E).
                    FileMenu            Selects File menu (Alt-F).
                    GetFindString       Opens a dialog box for the Search
                                        operation. (Alt-S F)
                    GotoWindow1         Selects window #1 (Alt-1).
                    GotoWindow2         Selects window #2 (Alt-2).
                    GotoWindow3         Selects window #3 (Alt-3).
                    GotoWindow4         Selects window #4 (Alt-4).
                    GotoWindow5         Selects window #5 (Alt-5).
                    GotoWindow6         Selects window #6 (Alt-6).
                    GotoWindow7         Selects window #7 (Alt-7).
                    GotoWindow8         Selects window #8 (Alt-8).
                    GotoWindow9         Selects window #9 (Alt-9).
                    Help                Opens the Help window (F1).
                    HelpMenu            Selects Help menu (Alt-H).
                    HelpIndex           Display the Help system't index
                                        (Shift-F1)
                    Inspect             Inspects item (Alt-F4).
                    LastHelp            Opens previous help window (Alt-
                                        F1).
                    MakeProject         Makes project (F9).
                    Menu                Highlights top menu bar.




                                  - 73 -






                    Modify              Evaluates expression/modify
                                        variable (Ctrl-F4).
                    NextError           Moves to next item in message
                                        window (Alt-F8).
                    NextWindow          Selects next window in IDE (F6).
                    OpenFile            Opens dialog box for File Open
                                        (F3).
                    OptionsMenu         Selects Options menu (Alt-O).
                    PrevError           Moves to previous item in message
                                        window (Alt-F7).
                    PrintBlock          Writes selected text to the
                                        printer.
                    ProjectMenu         Selects Project menu (Alt-P).
                    Quit                Exits the IDE (Alt-X).
                    ReadBlock           Opens dialog box requesting a file
                                        name to be read into the buffer at
                                        the cursor location and marked as
                                        selected text.
                    RepeatSearch        Searches again, using previous
                                        parameters.
                    Replace             Opens an dialog box for the Replace
                                        operation.
                    ResetProgram        Resets program being debugged
                                        (Ctrl-F2).
                    RunMenu             Selects Run menu (Alt-R).
                    RunProgram          Makes and runs current executable
                                        (Ctrl-F9).
                    RunToHere           Runs program until statement at
                                        cursor (F4).
                    SaveFile            Saves current editor buffer (F2).
                    SaveFileAs          Opens dialog for File SaveAs.
                    SearchMenu          Selects Search menu (Alt-S).
                    Step                Step over (F8).
                    SystemMenu          Selects Sytem menu (Alt-Spacebar).
                    ToggleBreakpoint    Sets/Clears a breakpoint at the
                                        cursor location
                    Trace               Trace into (F7).
                    Transfer0           Selects nth item from transfer menu
                    Transfer1           .
                    Transfer2           .
                    Transfer3           .
                    Transfer4           .
                    Transfer5           .
                    Transfer6           .
                    Transfer7           .
                    Transfer8           .
                    Transfer9           .
                    ViewCallStack       Views Call Stack (Ctrl-F3).



                                  - 74 -






                    ViewUserScreen      Displays User Screen (Alt-F5).
                    WindowList          Displays window list (Alt-0).
                    WindowMenu          Selects Window menu (Alt-W).
                    WindowCascade
                    WindowTile
                    WordHelp            Context sensitive help (Ctrl-F1).
                    WriteBlock          Opens dialog box requesting a file
                                        name to which the selected text
                                        will be written.
                    ZoomWindow          Zooms/unzoomd current window (F5).









































                                  - 75 -

























































                                  - 76 -






INDEX
___________________________________________________________________________





- + and + - (TLIB action symbols)       /C THELP option (select color) 39,
  48                                      40
[ ] GREP operator 17                    /C TLIB option (case sensitivity)
/? THELP help option 40, 42               45, 50
-* and ** (TLIB action symbols) 47      $CAP EDIT transfer macro 55
* (TLIB action symbol) 47               $CAP MSG transfer macro 55
+ (TLIB action symbol) 47               case sensitivity
- (TLIB action symbol) 47                 GREP option 14
$ GREP operator 17                        TLIB option 45, 50
* GREP operator 17                      characters
+ GREP operator 17                        trigraph
. GREP operator 17                          converting 53
\ GREP operator 17                      $COL transfer macro 56
^ GREP operator 17                      columns
? THELP option 40, 42                     numbers 56
                                        command line
                                          syntax
A                                           CPP 10
add (TLIB action symbol) 47             compilers
                                          Turbo editor macro 56
                                        $CONFIG transfer macro 56
B                                       configuration files 56
BGIOBJ 2-10                               TCC file 10
  advanced features 6                     CPP and 10
  command-line syntax 3, 6              conversion
  components 6                            trigraphs 53
  example 4                             CPP 10-12
  graphics.h and 8                        command-line options and syntax
  options                                 10
    destination file 7                    directory 10
    /F 6                                  example of use 11
    file name 6                           files
    file name (/F) 5                        compiling 11
    public name 7                         -P option (source file names and
    segment class 7                         line numbers) 11
    segment name 7                        wildcards and 10
    source file 7

                                        D
C                                       -d GREP option (directories) 14
-c GREP option (count only) 14          /D OBJXREF option (directory) 24



Index                                                                    77






debugging                               extended dictionary
  include files 10                        TLIB and 45, 49
  macros 10                             extract and remove (TLIB action) 47
$DEF transfer macro 56
Defines option
  transfer macro 56                     F
$DEP transfer macro 56                  /F BGIOBJ option 6
dependencies                            /F BGIOBJ option (far routines) 5
  explicit 56                           /F OBJXREF option (include full
$DIR transfer macro 56                    library) 25
directories                             /F THELP option 41
  CPP 10                                /F THELP option (Help file path and
  GREP option 14                          name) 40
  include files                         files
    transfer macro 57                     dates
  libraries                                 changing 51
    transfer macro 57                     destination
  .OBJ files 24                             BGIOBJ 7
  transfer macro 56                       extensions 57
DLLs                                      linker response, used by OBJXREF
  creating 57                               28, 33
DOS                                       macros
  commands                                  expanded 10
    running from the IDE 59               matching
$DRIVE transfer macro 56                    GREP option 14
                                          names
                                            macros
E                                             transfer 54
/E TLIB option (extended                    printing (GREP) 15
  dictionary) 45, 49                      output, generated by OBJXREF 25
editor                                    path
  macro language (TEML)                     macros 56
    using 56                              saving
$EDNAME transfer macro 56                   all 58
$ERRCOL transfer macro 57                 searching 12-22
$ERRLINE transfer macro 57                source
$ERRNAME transfer macro 57                  BGIOBJ 7
errors                                  filters 55
  linker                                  GREP 56
    graphics drivers and fonts 5          Resource Compiler 56
  OBJXREF (list) 34                       TASM 56
examples                                fonts
  OBJXREF 28-33                           adding to graphics library 3
.EXE files                                files, converting to .OBJ files 2
  file name transfer macro 57             included with Turbo C++ 4
$EXENAME transfer macro 57                linker errors and 5
$EXT transfer macro 57                    linking 2-10
                                          registering 3, 8



                                  - 78 -






  stroked 2-10                              white space in 18
    linking 2                             using 13
                                          wildcards and 18
                                        GREP.COM 16
G                                       GREP2MSG.EXE 56
graphics drivers
  adding to graphics library 3
  converting to .OBJ files 2, 2-10      H
  included with Turbo C++ 4             /H THELP option (help) 40, 42
  linker                                header files
    errors and 5                          graphics.h 8
  linking 2                             help
  registering 3, 8                        GREP (file searcher) 13
graphics.h (header file)                  OBJXREF 23
  BGIOBJ and 8                            THELP 40, 42
GRAPHICS.LIB                            hot keys
  adding to 3                             scan codes 42
GREP (file searcher) 12-22
  capturing messages 56
  examples 18                           I
  files to search 18                    -i GREP option (case sensitivity)
  help 13                                 14
  literal character 17                  /I OBJXREF option (case
  matches 17                              sensitivity) 24
  operators 17                          $INC transfer macro 57
  optimizing use of 16                  include files
  options                                 debugging 10
    case sensitivity (-i) 14              directories
    count only (-c) 14                      transfer macro 57
    default 15, 16                      integrated environment
    discussion 13                         DOS commands and 59
    file names (printing) 15              memory needs 59
    -i (case sensitivity) 14
    line numbers (-n) 14
    lines, nonmatching (-v) 15          K
    list matching files (-l) 14         /K THELP option (change hot key)
    -n (line numbers) 14                  40, 42
    -o (UNIX output format) 14          keyboard
    precedence 16                         trigraph program 53
    regular expression search (-r)
    14
    UNIX format (-o) 14                 L
    updating (-u) 15                    -l GREP option (list matching
    -v 15                                 files) 14
    -v (nonmatching lines) 15           /L OBJXREF command (linker response
    verbose 15                            file) 28
    word search (-w) 15                 $LIB transfer macro 57
  search strings 16                     libname (TLIB option) 45



Index                                                                    79






libraries                                 column number 57
  directories                             file name 57
    transfer macro 57                     line number 57
  files 22                              module names, TLIB 46
    contents of 22
  graphics
    adding driver and font files to     N
    3                                   -n command-line compiler option
  object files 43, 44                     CPP and 10
    creating 47                         -n GREP option (line numbers) 14
  OBJXREF                               /N OBJXREF option (limit reports)
    including all 25                      26
  page size 49                          $NAME transfer macro 57
$LINE transfer macro 57                 $NOSWAP transfer macro 57
lines                                   numbers
  numbering 57                            column 56
    printing (GREP) 14                    line 57
linker
  error: segment exceeds 64K 5
  response files                        O
    used by OBJXREF 28, 33              -o GREP option (UNIX format output)
linking                                   14
  graphics drivers 2                    /O OBJXREF option (output file for
listfile (TLIB option) 45                 reports) 25
                                        .OBJ files
                                          converting font files into 2
M                                         converting graphics drivers files
macros                                    into 2
  CPP and 11                              defined 22
  debugging 10                            directories 24
  editor 56                               libraries
  expanded                                  advantages of using 44
    list of 10                              creating 47
  preprocessing 11                          TLIB and 43
  preprocessor 10                         names 22
MAKE (program manager)                    response files and 27
  modifying 51                          object modules
  project files and 36                    defined 22
$MEM transfer macro 57, 59                names 22
memory                                  OBJXREF 22-34
  requirements                            directories 24
    IDE 59                                error messages 34
  transfer programs 57, 59                examples of reports 29, 30, 31,
Message window                            32
  capturing output into 55                help 23
  copying text from 59                    /L command (linker response
messages                                  files) 28
  capturing from programs 55



                                  - 80 -






  linker files                            wildcards and 23
    as response files 28                operations (TLIB option) 45
  options 23                            operators
    /N (limit information) 26             GREP 17
    /RV 26                              $OUTNAME transfer macro 57
    /RC 31                              output
    control 24                            capturing 55
    directories (/D) 24                 output file
    /F (include full library) 25          generated by OBJXREF 25
    ignore case (/I) 24
    include full library (/F) 25
    include zero-length segment         P
    definitions (/Z) 25                 -P CPP option (source file names
    list file names (/V) 25               and line numbers) 11
    modified reports 26                 /P OBJXREF command (project files)
    /O (output file) 25                   27
    reports 25                          /P TLIB option (page size) 49
      by class type                     page size (libraries) 49
        example 33                      path
      by class type (/RC) 26, 31          transfer macro 57
      by external reference (/RX)       precedence
      26, 30                              GREP options 16
      by module (/RM) 26, 29              TLIB commands 46
      by public names (/RP) 26, 29      PRJ2MAK (project file converter) 36
      by reference (/RR) 26, 30, 33     PRJCNVT (project file converter) 35
      default type 33                   $PRJNAME transfer macro 58
      of all types (/RV) 26             programs
      of module sizes (/RS) 26, 31        capturing output 55
      of unreferenced symbol names        file name 57
      (/RU) 26, 32                        memory assignments 59
      output file (/O) 25               project files
      verbose                             OBJXREF and 27
        example 33                        used by OBJXREF 33
      verbose (/RV) 26, 32              projects
    /V (verbose output) 25                files
    verbose report (/RV) 32                 converting from old versions 35
    /Z (include zero-length segment         converting to MAKE files 36
    definitions) 25                         file name transfer macro 58
  project files                         $PROMPT transfer macro 58
    as response files 27                public names
  project files (/P) 27                   defined 22
  reports 23
    modifying 26
    output file for (/O) 25             R
  response files 23, 27                 -r GREP option (regular expression
    example 33                            search) 14
    linker 28                           /RC OBJXREF option (report) 31
  warnings 34                           /RC OBJXREF option (reports) 26



Index                                                                    81






redirecting program output 55               in text files 12-22
registerbgidriver (function)            swapping
  BGIOBJ and 3, 8                         to User Screen 57
registerbgifont (function)              syntax
  BGIOBJ and 3, 8                         CPP 10
registerfarbgidriver (function)           TLIB 44
  BGIOBJ and 6, 8
registerfarbgifont (function)
  BGIOBJ and 6, 8                       T
remove (TLIB action) 47                 TASM2MSG.EXE 56
replace (TLIB action) 48                $TASM transfer macro 58
Resource Compiler                       text
  capturing messages 56                   Message window 59
resources                               text files
  rebuilding automatically 56             searching 12-22
response files                          THELP (Turbo Help utility) 37-43
  file-name extensions and 27             additional Help on highlighted
  formats 27                              word 39
  free-form 27                            cursor keys 38
    example 33                            index 39
  linker files and 28                     invoking 37
  OBJXREF and 23, 27, 28                  keywords 38
    example 33                            loading 37
  project files and 27                    options 39-43
  TLIB 48                                   colors (/C) 40
  TLINK, OBJXREF and 33                     colors (/C)  39
/RM OBJXREF option (reports) 26             Help file path (/F) 40
/RP OBJXREF option (reports) 26             help file path (/F) 41
/RR OBJXREF option (reports) 26             help on (/?
/RS OBJXREF option (reports) 26               /H
/RU OBJXREF option (reports) 26                 ?) 40, 42
/RV OBJXREF option (reports) 26             Help screen colors (/C) 39
/RX OBJXREF option (reports) 26             help screen colors (/C) 40
                                            hot keys (/K) 40, 42
                                            reassign hot keys (/K) 40, 42
S                                           removing THELP (/U) 40, 43
$SAVE ALL transfer macro 58                 screen colors (/C) 39, 40
$SAVE CUR transfer macro 58                 /U (removing THELP) 40, 43
$SAVE PROMPT transfer macro 58              /W (window options) 40
scan codes 42                               window options 43
searches                                    window options (/W) 40
  text files 12-22                        paging through 38
source files                              paste page 39
  separately compiled 44                  previous screens 39
standalone utilities 1                    quitting 39
strings                                   removing from memory 40, 43
  searching for                           scan codes 42
    as expressions (GREP) 14              using 38



                                  - 82 -






TLIB (librarian) 43-51                    $INC 57
  action symbols 46-48                    instruction 55
  capabilities 43                         $LIB 57
  examples 51                             $LINE 57
  extended dictionary (/e)                $MEM 57
    TLINK and 49                          $NAME 57
  module names 46                         $NOSWAP 57
  operations 46                           $OUTNAME 57
    precedence 46                         $PRJNAME 58
  options                                 $PROMPT 58
    case sensitivity (/c) 45, 50          $SAVE ALL 58
    /E 45, 49                             $SAVE CUR 58
    extended dictionary (/e) 45, 49       $SAVE PROMPT 58
    libname 45                            $TASM 58
    listfile 45                           $WRITEMSG 59
    operations 45                       trigraphs
    page size (/P) 49                     translating 53
    using 44                              undo option (-u) 53
  response files                        Turbo Assembler
    using 48                              capturing messages 56
  syntax 44                               $TASM macro 58
TLIB (Turbo librarian) 43-51            TURBOC.CFG 10
  options 39-51
TLINK (linker)
  response files                        U
    OBJXREF and 28                      -u GREP option (updating) 15
  TLIB extended dictionary and 49       /U THELP option 43
TOUCH 51, 52                            /U THELP option (remove THELP) 40
transfer macros                         UNIX
  $CAP EDIT 55                            format (GREP) 14
  $CAP MSG 55                           User Screen 57
  $COL 56                               utilities
  $CONFIG 56                              standalone 1
  $DEF 56                                 TLIB 43-51
  defined 54                              TOUCH 51
  $DEP 56
  $DIR 56
  DOS commands 59                       V
  $DRIVE 56                             -v GREP option (nonmatching lines)
  $EDNAME 56                              15
  $ERRCOL 57                            /V OBJXREF option (verbose output)
  $ERRLINE 57                             25
  $ERRNAME 57
  $EXENAME 57
  $EXT 57                               W
  file names 54                         -w GREP option (word search) 15
  glossary of 55                        /W THELP option (ser window
  how expanded 54                         size/location) 40



Index                                                                    83






/W THELP option (window options) 43     Z
wildcards                               -z GREP option (verbose) 15
  CPP and 10                            /Z OBJXREF option (include zero-
  OBJXREF and 23                          length segment definitions) 25
  TOUCH and 52
$WRITEMSG transfer macro 59













































                                  - 84 -

