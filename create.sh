#!/bin/bash

# Update package list and install required packages
sudo apt update
sudo apt install -y sublime-text dosbox code openssh-server

# Copy folders to home directory
cp -r ./qb45 ~/qb45
cp -r ./TurboC ~/TurboC

# Create first launcher for QB45
echo "[Desktop Entry]
Name=QB45
Exec=dosbox ~/qb45/QB45/qb.exe
Type=Application
Terminal=false
Icon=utilities-terminal
Categories=Development;" > ~/Desktop/QB45.desktop

# Create second launcher for TurboC
echo "[Desktop Entry]
Name=TurboC
Exec=dosbox ~/TurboC/Turbo.C.3.2/WinRoot/BIN/TC.EXE
Type=Application
Terminal=false
Icon=utilities-terminal
Categories=Development;" > ~/Desktop/TurboC.desktop

# Make the launchers executable
chmod +x ~/Desktop/QB45.desktop
chmod +x ~/Desktop/TurboC.desktop

echo "Installation and setup complete."