<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Turbo_C__.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7"/>
    </startup>
    <userSettings>
        <Turbo_C__.Properties.Settings>
            <setting name="KeepWindowOpen" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="FullScreenMode" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="IsFirstTime" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="Recent1" serializeAs="String">
                <value/>
            </setting>
            <setting name="Recent2" serializeAs="String">
                <value/>
            </setting>
            <setting name="Recent3" serializeAs="String">
                <value/>
            </setting>
            <setting name="Recent1Name" serializeAs="String">
                <value/>
            </setting>
            <setting name="Recent2Name" serializeAs="String">
                <value/>
            </setting>
            <setting name="Recent3Name" serializeAs="String">
                <value/>
            </setting>
            <setting name="LiveExample" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="LiveExampleDate" serializeAs="String">
                <value>2015-09-24</value>
            </setting>
        </Turbo_C__.Properties.Settings>
    </userSettings>
</configuration>
